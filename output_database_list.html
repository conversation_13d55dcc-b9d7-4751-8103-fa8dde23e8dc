<html>
            <head>
                <title>Database Selection</title><meta charset="utf-8"></meta>
                <meta name="viewport" content="width=device-width, initial-scale=1"></meta>
                <link rel="stylesheet" href="/static/css/database_list.css"></link>
                <style>
                    body {
                        font-family: &#x27;Segoe UI&#x27;, Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    h1 {
                        color: #333;
                        text-align: center;
                        margin-bottom: 30px;
                    }
                    #database-list {
                        margin-top: 20px;
                    }
                    .loading {
                        text-align: center;
                        color: #666;
                        font-style: italic;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>Database Selection</h1><p>Select a database to continue:</p>
                    <p>Single database mode</p>
                    <div id="database-list">
                        <div class="loading">Loading databases...</div>
                    </div>
                    
                    <div style="margin-top: 30px; text-align: center;">
                        <small style="color: #666;">
                            ERP System v1.0 - 
                            <span>Multi-DB</span></small>
                    </div>
                </div>
                
                <script>
                    // Load database list via API
                    async function loadDatabases() {
                        try {
                            const response = await fetch(&#x27;/api/databases&#x27;);
                            const result = await response.json();
                            
                            const container = document.getElementById(&#x27;database-list&#x27;);
                            
                            if (result.success &amp;&amp; result.data.length &gt; 0) {
                                container.innerHTML = result.data.map(function(db) {
                                    return &#x27;&lt;div style=&quot;border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 4px; cursor: pointer; transition: background-color 0.2s;&quot; &#x27; +
                                           &#x27;onclick=&quot;selectDatabase(\&#x27;&#x27; + db.name + &#x27;\&#x27;)&quot; &#x27; +
                                           &#x27;onmouseover=&quot;this.style.backgroundColor=\&#x27;#f0f0f0\&#x27;&quot; &#x27; +
                                           &#x27;onmouseout=&quot;this.style.backgroundColor=\&#x27;white\&#x27;&quot;&gt;&#x27; +
                                        &#x27;&lt;h3 style=&quot;margin: 0 0 10px 0; color: #333;&quot;&gt;&#x27; + db.name + &#x27;&lt;/h3&gt;&#x27; +
                                        &#x27;&lt;p style=&quot;margin: 5px 0; color: #666;&quot;&gt;Size: &#x27; + db.size + &#x27;&lt;/p&gt;&#x27; +
                                        &#x27;&lt;p style=&quot;margin: 5px 0; color: #666;&quot;&gt;Status: &#x27; + db.status + &#x27;&lt;/p&gt;&#x27; +
                                    &#x27;&lt;/div&gt;&#x27;;
                                }).join(&#x27;&#x27;);
                            } else {
                                container.innerHTML = &#x27;<p style="text-align: center; color: #666;">No databases found</p>&#x27;;
                            }
                        } catch (error) {
                            console.error(&#x27;Error loading databases:&#x27;, error);
                            document.getElementById(&#x27;database-list&#x27;).innerHTML = 
                                &#x27;<p style="text-align: center; color: #d32f2f;">Error loading databases</p>&#x27;;
                        }
                    }
                    
                    function selectDatabase(dbName) {
                        window.location.href = &#x27;/app?db=&#x27; + encodeURIComponent(dbName);
                    }
                    
                    // Load databases when page loads
                    document.addEventListener(&#x27;DOMContentLoaded&#x27;, loadDatabases);
                </script>
            </body>
        </html>
    