"""
Tests for ir.module.module model in base addon
Demonstrates proper ERP test structure
"""
import sys
import os

# Add ERP core to path for testing
erp_path = os.path.join(os.path.dirname(__file__), '..', '..', '..')
if erp_path not in sys.path:
    sys.path.insert(0, erp_path)

import pytest
from unittest.mock import AsyncMock, patch
from erp.tests.common import AsyncTransactionCase, AsyncSingleTransactionCase
from erp.tests.tags import tag, Tags
from erp.addons import get_addon_module


@tag(Tags.UNIT, Tags.BASE)
class TestIrModuleModule:
    """Test cases for ir.module.module model"""
    
    def setup_method(self):
        """Set up test case"""
        # Ensure base addon is loaded
        base_addon = get_addon_module('base')
        assert base_addon is not None, "Base addon should be available"

        # Import the async test environment
        from erp.tests.common import AsyncTestEnvironment
        self.env = AsyncTestEnvironment()

        # Get the model class
        self.IrModuleModule = self.env['ir.module.module']

    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_create_module_record(self, mock_get_db):
        """Test creating a module record"""
        # Mock database
        mock_db = AsyncMock()
        mock_db.insert.return_value = 'test-module-id'
        mock_get_db.return_value = mock_db

        module_data = {
            'name': 'test_module',
            'display_name': 'Test Module',
            'summary': 'A test module',
            'description': 'This is a test module for unit testing',
            'author': 'Test Author',
            'version': '1.0.0',
            'category': 'Test',
            'state': 'uninstalled'
        }

        module = await self.IrModuleModule.create(module_data)

        assert module.name == 'test_module'
        assert module.display_name == 'Test Module'
        assert module.summary == 'A test module'
        assert module.author == 'Test Author'
        assert module.version == '1.0.0'
        assert module.category == 'Test'
        assert module.state == 'uninstalled'
    
    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_module_name_required(self, mock_get_db):
        """Test that module name is required"""
        # Mock database
        mock_db = AsyncMock()
        mock_get_db.return_value = mock_db

        with pytest.raises(Exception):
            await self.IrModuleModule.create({
                'display_name': 'Test Module',
                'state': 'uninstalled'
            })

    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_module_display_name_required(self, mock_get_db):
        """Test that module display name is required"""
        # Mock database
        mock_db = AsyncMock()
        mock_get_db.return_value = mock_db

        with pytest.raises(Exception):
            await self.IrModuleModule.create({
                'name': 'test_module',
                'state': 'uninstalled'
            })

    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_module_state_values(self, mock_get_db):
        """Test valid module state values"""
        # Mock database
        mock_db = AsyncMock()
        mock_db.insert.return_value = 'test-id'
        mock_get_db.return_value = mock_db

        valid_states = ['uninstalled', 'installed', 'to_install', 'to_upgrade', 'to_remove']

        for state in valid_states:
            module = await self.IrModuleModule.create({
                'name': f'test_module_{state}',
                'display_name': f'Test Module {state}',
                'state': state
            })
            assert module.state == state
    
    @tag(Tags.SLOW)
    @pytest.mark.asyncio
    async def test_module_search_by_category(self):
        """Test searching modules by category"""
        # Create test modules in different categories
        categories = ['Test', 'Demo', 'Custom']
        modules = []

        for i, category in enumerate(categories):
            module = await self.IrModuleModule.create({
                'name': f'test_module_{i}',
                'display_name': f'Test Module {i}',
                'category': category,
                'state': 'uninstalled'
            })
            modules.append(module)

        # Search for modules in 'Test' category
        test_modules = await self.IrModuleModule.search([('category', '=', 'Test')])
        assert len(test_modules) >= 1

        # Verify all found modules are in Test category
        for module in test_modules:
            assert module.category == 'Test'


@tag(Tags.INTEGRATION, Tags.BASE)
class TestIrModuleModuleIntegration:
    """Integration tests for ir.module.module model"""

    def setup_method(self):
        """Set up test case"""
        # Ensure base addon is loaded
        base_addon = get_addon_module('base')
        assert base_addon is not None, "Base addon should be available"

        # Import the async test environment
        from erp.tests.common import AsyncTestEnvironment
        self.env = AsyncTestEnvironment()

        # Get the model class
        self.IrModuleModule = self.env['ir.module.module']

    @pytest.mark.asyncio
    async def test_module_dependency_chain(self):
        """Test module dependency relationships"""
        # Create a chain of dependent modules
        base_module = await self.IrModuleModule.create({
            'name': 'base_test',
            'display_name': 'Base Test Module',
            'state': 'installed'
        })

        dependent_module = await self.IrModuleModule.create({
            'name': 'dependent_test',
            'display_name': 'Dependent Test Module',
            'depends': 'base_test',
            'state': 'uninstalled'
        })

        assert base_module.state == 'installed'
        assert dependent_module.state == 'uninstalled'
        assert dependent_module.depends == 'base_test'
    
    @pytest.mark.asyncio
    async def test_module_lifecycle_states(self):
        """Test module lifecycle state transitions"""
        module = await self.IrModuleModule.create({
            'name': 'lifecycle_test',
            'display_name': 'Lifecycle Test Module',
            'state': 'uninstalled'
        })

        # Test state transitions
        assert module.state == 'uninstalled'

        # Mark for installation
        module.state = 'to_install'
        assert module.state == 'to_install'

        # Install
        module.state = 'installed'
        assert module.state == 'installed'

        # Mark for upgrade
        module.state = 'to_upgrade'
        assert module.state == 'to_upgrade'

        # Back to installed
        module.state = 'installed'
        assert module.state == 'installed'

        # Mark for removal
        module.state = 'to_remove'
        assert module.state == 'to_remove'

        # Uninstall
        module.state = 'uninstalled'
        assert module.state == 'uninstalled'


@tag(Tags.UNIT, Tags.BASE, 'model_validation')
class TestIrModuleModuleValidation:
    """Test validation logic for ir.module.module model"""

    def setup_method(self):
        """Set up test case"""
        # Import the async test environment
        from erp.tests.common import AsyncTestEnvironment
        self.env = AsyncTestEnvironment()
        self.IrModuleModule = self.env['ir.module.module']

    @pytest.mark.asyncio
    async def test_module_name_format(self):
        """Test module name format validation"""
        # Valid names
        valid_names = ['test_module', 'my_addon', 'sale_management', 'hr_payroll']

        for name in valid_names:
            module = await self.IrModuleModule.create({
                'name': name,
                'display_name': f'Test {name}',
                'state': 'uninstalled'
            })
            assert module.name == name

    @pytest.mark.asyncio
    async def test_module_version_format(self):
        """Test module version format"""
        valid_versions = ['1.0.0', '2.1.3', '10.0.1.2.3', '1.0']

        for version in valid_versions:
            module = await self.IrModuleModule.create({
                'name': f'test_module_{version.replace(".", "_")}',
                'display_name': f'Test Module {version}',
                'version': version,
                'state': 'uninstalled'
            })
            assert module.version == version

    @pytest.mark.asyncio
    async def test_module_unique_name(self):
        """Test that module names must be unique"""
        # Create first module
        await self.IrModuleModule.create({
            'name': 'unique_test',
            'display_name': 'Unique Test Module',
            'state': 'uninstalled'
        })

        # Try to create another module with same name
        with pytest.raises(Exception):
            await self.IrModuleModule.create({
                'name': 'unique_test',
                'display_name': 'Another Unique Test Module',
                'state': 'uninstalled'
            })
