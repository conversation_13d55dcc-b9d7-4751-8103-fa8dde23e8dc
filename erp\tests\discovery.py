"""
Test discovery system for ERP testing framework
Provides comprehensive test discovery across addons and core modules
"""
import os
import sys
import importlib
import importlib.util
import inspect
from typing import List, Dict, Set, Type, Optional, Union
from pathlib import Path

from .common import TestCase, TransactionCase, SingleTransactionCase
from .tags import get_test_tags, should_run_test


class TestDiscovery:
    """Test discovery engine for ERP system"""
    
    def __init__(self, addons_path: str = 'addons'):
        self.addons_path = addons_path
        self.discovered_tests: Dict[str, List[Type[TestCase]]] = {}
        self.test_modules: Dict[str, object] = {}
    
    def discover_all_tests(self) -> Dict[str, List[Type[TestCase]]]:
        """Discover all tests in the system"""
        # Discover core tests
        self.discover_core_tests()
        
        # Discover addon tests
        self.discover_addon_tests()
        
        return self.discovered_tests
    
    def discover_core_tests(self, test_path: str = 'tests') -> List[Type[TestCase]]:
        """Discover tests in core test directory"""
        if not os.path.exists(test_path):
            return []
        
        test_classes = []
        for root, dirs, files in os.walk(test_path):
            # Skip __pycache__ directories
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    module_path = os.path.join(root, file)
                    test_classes.extend(self._discover_tests_in_file(module_path))
        
        self.discovered_tests['core'] = test_classes
        return test_classes
    
    def discover_addon_tests(self, addon_names: List[str] = None) -> Dict[str, List[Type[TestCase]]]:
        """Discover tests in addon directories"""
        if not os.path.exists(self.addons_path):
            return {}
        
        addon_tests = {}
        
        # Get list of addons to check
        if addon_names is None:
            addon_names = [
                name for name in os.listdir(self.addons_path)
                if os.path.isdir(os.path.join(self.addons_path, name))
                and not name.startswith('.')
            ]
        
        for addon_name in addon_names:
            addon_path = os.path.join(self.addons_path, addon_name)
            tests_path = os.path.join(addon_path, 'tests')
            
            if os.path.exists(tests_path):
                test_classes = self._discover_addon_test_directory(tests_path, addon_name)
                if test_classes:
                    addon_tests[addon_name] = test_classes
                    self.discovered_tests[addon_name] = test_classes
        
        return addon_tests
    
    def _discover_addon_test_directory(self, tests_path: str, addon_name: str) -> List[Type[TestCase]]:
        """Discover tests in an addon's test directory"""
        test_classes = []
        
        for root, dirs, files in os.walk(tests_path):
            # Skip __pycache__ directories
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    module_path = os.path.join(root, file)
                    test_classes.extend(self._discover_tests_in_file(module_path, addon_name))
        
        return test_classes
    
    def _discover_tests_in_file(self, file_path: str, addon_name: str = None) -> List[Type[TestCase]]:
        """Discover test classes in a specific file"""
        test_classes = []
        
        try:
            # Convert file path to module name
            module_name = self._file_path_to_module_name(file_path, addon_name)
            
            # Import the module
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                sys.modules[module_name] = module
                spec.loader.exec_module(module)
                
                self.test_modules[module_name] = module
                
                # Find test classes in the module
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    if (issubclass(obj, TestCase) and 
                        obj is not TestCase and 
                        obj is not TransactionCase and 
                        obj is not SingleTransactionCase):
                        test_classes.append(obj)
        
        except Exception as e:
            print(f"Warning: Failed to discover tests in {file_path}: {e}")
        
        return test_classes
    
    def _file_path_to_module_name(self, file_path: str, addon_name: str = None) -> str:
        """Convert file path to Python module name"""
        # Normalize path separators
        file_path = file_path.replace('\\', '/')
        
        # Remove .py extension
        if file_path.endswith('.py'):
            file_path = file_path[:-3]
        
        # Convert to module name
        if addon_name:
            # For addon tests: erp.addons.addon_name.tests.test_module
            relative_path = file_path.replace(f'{self.addons_path}/{addon_name}/', '')
            module_name = f'erp.addons.{addon_name}.{relative_path.replace("/", ".")}'
        else:
            # For core tests: erp.tests.test_module
            relative_path = file_path.replace('tests/', '')
            module_name = f'erp.tests.{relative_path.replace("/", ".")}'
        
        return module_name
    
    def filter_tests_by_tags(self, test_classes: List[Type[TestCase]], 
                           include_tags: Set[str] = None, 
                           exclude_tags: Set[str] = None) -> List[Type[TestCase]]:
        """Filter test classes by tags"""
        filtered_tests = []
        
        for test_class in test_classes:
            if should_run_test(test_class, include_tags, exclude_tags):
                filtered_tests.append(test_class)
        
        return filtered_tests
    
    def filter_tests_by_pattern(self, test_classes: List[Type[TestCase]], 
                              pattern: str) -> List[Type[TestCase]]:
        """Filter test classes by name pattern"""
        filtered_tests = []
        
        for test_class in test_classes:
            if pattern.lower() in test_class.__name__.lower():
                filtered_tests.append(test_class)
        
        return filtered_tests
    
    def get_test_methods(self, test_class: Type[TestCase]) -> List[str]:
        """Get all test methods from a test class"""
        test_methods = []
        
        for name in dir(test_class):
            if name.startswith('test_') and callable(getattr(test_class, name)):
                test_methods.append(name)
        
        return test_methods
    
    def get_all_discovered_tests(self) -> List[Type[TestCase]]:
        """Get all discovered test classes"""
        all_tests = []
        for test_list in self.discovered_tests.values():
            all_tests.extend(test_list)
        return all_tests
    
    def get_tests_by_addon(self, addon_name: str) -> List[Type[TestCase]]:
        """Get tests for a specific addon"""
        return self.discovered_tests.get(addon_name, [])
    
    def list_available_addons_with_tests(self) -> List[str]:
        """List all addons that have tests"""
        return [
            addon_name for addon_name in self.discovered_tests.keys()
            if addon_name != 'core' and self.discovered_tests[addon_name]
        ]
    
    def get_test_statistics(self) -> Dict[str, int]:
        """Get statistics about discovered tests"""
        stats = {
            'total_test_classes': 0,
            'total_test_methods': 0,
            'addons_with_tests': 0,
            'core_test_classes': 0
        }
        
        for addon_name, test_classes in self.discovered_tests.items():
            stats['total_test_classes'] += len(test_classes)
            
            if addon_name == 'core':
                stats['core_test_classes'] = len(test_classes)
            else:
                if test_classes:
                    stats['addons_with_tests'] += 1
            
            # Count test methods
            for test_class in test_classes:
                stats['total_test_methods'] += len(self.get_test_methods(test_class))
        
        return stats
