#!/usr/bin/env python3
"""
Test script for addon lifecycle hooks and Environment system
"""
import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from erp.environment import EnvironmentManager, create_env
from erp.context import Con<PERSON><PERSON>anager
from erp.addons.loader import <PERSON><PERSON><PERSON>oader
from erp.addons.hooks import get_hook_registry, HookType
from erp.config import config


async def test_environment_creation():
    """Test Environment creation and context management"""
    print("=== Testing Environment Creation ===")
    
    try:
        # Create environment
        db_name = config.get_default_database()
        env = await create_env(db_name, 1, {'test': True, 'lang': 'en_US'})
        
        print(f"Created environment: {env}")
        print(f"Database: {env.cr.db_name}")
        print(f"User ID: {env.uid}")
        print(f"Context: {env.context}")
        
        # Test context management
        async with ContextManager.with_context(env=env):
            current_env = ContextManager.get_environment()
            print(f"Current environment from context: {current_env}")
            
            # Test with_context method
            new_env = env.with_context(test_mode=True, debug=True)
            print(f"New environment with context: {new_env}")
            print(f"New context: {new_env.context}")
        
        print("✓ Environment creation test passed")
        
    except Exception as e:
        print(f"✗ Environment creation test failed: {e}")
        import traceback
        traceback.print_exc()


async def test_lifecycle_hooks():
    """Test addon lifecycle hooks"""
    print("\n=== Testing Lifecycle Hooks ===")
    
    try:
        # Create environment for hooks
        db_name = config.get_default_database()
        env = await create_env(db_name, 1, {'test': True})
        
        # Get hook registry
        hook_registry = get_hook_registry()
        
        # Test hook execution
        print("Testing hook execution...")
        
        # Execute pre-install hooks
        await hook_registry.execute_hooks(HookType.PRE_INSTALL, 'test_addon', env)
        print("✓ Pre-install hooks executed")
        
        # Execute post-install hooks
        await hook_registry.execute_hooks(HookType.POST_INSTALL, 'test_addon', env)
        print("✓ Post-install hooks executed")
        
        # Execute pre-uninstall hooks
        await hook_registry.execute_hooks(HookType.PRE_UNINSTALL, 'test_addon', env)
        print("✓ Pre-uninstall hooks executed")
        
        # Execute post-uninstall hooks
        await hook_registry.execute_hooks(HookType.POST_UNINSTALL, 'test_addon', env)
        print("✓ Post-uninstall hooks executed")
        
        print("✓ Lifecycle hooks test passed")
        
    except Exception as e:
        print(f"✗ Lifecycle hooks test failed: {e}")
        import traceback
        traceback.print_exc()


async def test_addon_loading_with_hooks():
    """Test addon loading with lifecycle hooks"""
    print("\n=== Testing Addon Loading with Hooks ===")
    
    try:
        # Create environment
        db_name = config.get_default_database()
        env = await create_env(db_name, 1, {'test': True})
        
        # Create addon loader
        loader = AddonLoader()
        
        # Discover addons
        available_addons = await loader.discover_addons()
        print(f"Available addons: {list(available_addons.keys())}")
        
        # Test loading example_hooks addon if available
        if 'example_hooks' in available_addons:
            print("Loading example_hooks addon...")
            
            async with ContextManager.with_context(env=env):
                await loader._load_addon('example_hooks', available_addons['example_hooks'], env)
            
            print("✓ Addon loaded with hooks")
            
            # Test unloading
            print("Unloading example_hooks addon...")
            async with ContextManager.with_context(env=env):
                await loader.unload_addon('example_hooks', env)
            
            print("✓ Addon unloaded with hooks")
        else:
            print("example_hooks addon not found, skipping addon loading test")
        
        print("✓ Addon loading with hooks test passed")
        
    except Exception as e:
        print(f"✗ Addon loading with hooks test failed: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Main test function"""
    print("Starting ERP Lifecycle Hooks and Environment Tests")
    print("=" * 60)
    
    await test_environment_creation()
    await test_lifecycle_hooks()
    await test_addon_loading_with_hooks()
    
    print("\n" + "=" * 60)
    print("Tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
