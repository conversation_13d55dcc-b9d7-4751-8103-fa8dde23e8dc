"""
Test management commands
"""
import argparse
import os
import subprocess
import sys
from typing import List, Optional

from .base import BaseCommand, CommandGroup
from ..config import config


class TestCommand(BaseCommand):
    """Main test command with comprehensive Odoo-like features"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        # Test enabling (Odoo-style)
        parser.add_argument('--test-enable', action='store_true',
                           help='Enable test mode (required for running tests)')
        
        # Module/addon selection
        parser.add_argument('-i', '--install', dest='modules', action='append',
                           help='Install and test specific modules (can be used multiple times)')
        parser.add_argument('-u', '--update', dest='update_modules', action='append',
                           help='Update and test specific modules (can be used multiple times)')
        parser.add_argument('--module', '-m', help='Test specific module (alias for -i)')
        
        # Test filtering
        parser.add_argument('--test-tags', help='Run tests with specific tags (comma-separated)')
        parser.add_argument('--test-exclude-tags', help='Exclude tests with specific tags (comma-separated)')
        parser.add_argument('--pattern', '-p', help='Test name pattern to match')
        
        # Test frameworks
        parser.add_argument('--framework', choices=['pytest', 'unittest', 'erp'],
                           default='erp', help='Test framework to use')
        parser.add_argument('--pytest-args', help='Additional arguments to pass to pytest')
        parser.add_argument('--unittest-args', help='Additional arguments to pass to unittest')
        
        # Test types
        parser.add_argument('--unit', action='store_true', help='Run only unit tests')
        parser.add_argument('--integration', action='store_true', help='Run only integration tests')
        parser.add_argument('--performance', action='store_true', help='Run performance tests')
        parser.add_argument('--async-tests', action='store_true', help='Run async tests')
        parser.add_argument('--all-tests', action='store_true', help='Run all tests including slow ones')
        
        # Output and reporting
        parser.add_argument('--coverage', action='store_true', help='Generate coverage report')
        parser.add_argument('--coverage-html', action='store_true', help='Generate HTML coverage report')
        parser.add_argument('--junit-xml', help='Generate JUnit XML report to specified file')
        parser.add_argument('--verbose', '-v', action='count', default=1, help='Increase verbosity')
        parser.add_argument('--quiet', '-q', action='store_true', help='Minimal output')
        parser.add_argument('--list-tests', action='store_true', help='List available tests without running')
        
        # Test database and environment
        parser.add_argument('--test-db', help='Test database name (default: erp_test)')
        parser.add_argument('--keep-db', action='store_true', help='Keep test database after tests')
        parser.add_argument('--recreate-db', action='store_true', help='Recreate test database before tests')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle test command with comprehensive Odoo-like features"""
        try:
            # Check if test mode is enabled (Odoo-style requirement)
            if not args.test_enable:
                self.print_error("Tests must be run with --test-enable flag")
                print("Example usage:")
                print("  erp-bin test --test-enable -i base")
                print("  erp-bin test --test-enable --test-tags unit")
                print("  erp-bin test --test-enable -i sale --test-tags integration")
                return 1
            
            # Setup test environment
            self._setup_test_environment(args)
            
            # Determine which framework to use
            if args.framework == 'pytest':
                return self._run_pytest_tests(args)
            elif args.framework == 'unittest':
                return self._run_unittest_tests(args)
            else:  # erp framework
                return self._run_erp_tests(args)
                
        except Exception as e:
            if args.verbose > 1:
                import traceback
                traceback.print_exc()
            else:
                self.print_error(f"Failed to run tests: {e}")
            return 1
    
    def _setup_test_environment(self, args: argparse.Namespace):
        """Setup test environment"""
        # Set test database name
        test_db = args.test_db or 'erp_test'
        config.set('options', 'db_name', test_db)
        
        # Setup test configuration
        if args.verbose > 1:
            self.print_info(f"Setting up test environment with database: {test_db}")
        
        # Recreate database if requested
        if args.recreate_db:
            if args.verbose > 1:
                self.print_info("Recreating test database...")
            # TODO: Implement database recreation
    
    def _run_pytest_tests(self, args: argparse.Namespace) -> int:
        """Run tests using pytest framework"""
        self.print_info("Running tests with pytest framework...")
        
        try:
            # Build pytest command
            cmd = ['python', '-m', 'pytest']
            
            # Add module/addon selection
            test_paths = ['tests/']
            if args.modules or args.update_modules or args.module:
                # Add addon test paths
                addon_names = []
                if args.modules:
                    addon_names.extend(args.modules)
                if args.update_modules:
                    addon_names.extend(args.update_modules)
                if args.module:
                    addon_names.append(args.module)
                
                for addon_name in addon_names:
                    addon_test_path = f"addons/{addon_name}/tests/"
                    if os.path.exists(addon_test_path):
                        test_paths.append(addon_test_path)
            
            # Add test selection based on args
            if args.pattern:
                cmd.extend(['-k', args.pattern])
            
            # Add tag-based filtering
            if args.test_tags:
                tags = args.test_tags.replace(',', ' or ')
                cmd.extend(['-m', tags])
            elif args.unit:
                cmd.extend(['-m', 'unit'])
            elif args.integration:
                cmd.extend(['-m', 'integration'])
            elif args.performance:
                cmd.extend(['-m', 'performance'])
            elif args.async_tests:
                cmd.extend(['-m', 'async_test'])
            elif not args.all_tests:
                # Skip slow tests by default
                cmd.extend(['-m', 'not slow'])
            
            if args.test_exclude_tags:
                exclude_tags = args.test_exclude_tags.replace(',', ' and not ')
                cmd.extend(['-m', f'not {exclude_tags}'])
            
            # Add coverage options
            if args.coverage:
                cmd.extend(['--cov=erp', '--cov-report=term-missing'])
            
            if args.coverage_html:
                cmd.extend(['--cov=erp', '--cov-report=html'])
            
            # Add JUnit XML output
            if args.junit_xml:
                cmd.extend(['--junit-xml', args.junit_xml])
            
            # Add verbosity
            if args.quiet:
                cmd.append('-q')
            else:
                cmd.extend(['-v'] * args.verbose)
            
            # Add additional pytest args
            if args.pytest_args:
                cmd.extend(args.pytest_args.split())
            
            # Add test paths
            cmd.extend(test_paths)
            
            if args.list_tests:
                cmd.append('--collect-only')
            
            self.print_info(f"Running: {' '.join(cmd)}")
            
            # Run pytest
            result = subprocess.run(cmd, cwd=os.path.dirname(__file__))
            return result.returncode
            
        except Exception as e:
            self.print_error(f"Failed to run pytest: {e}")
            return 1
    
    def _run_unittest_tests(self, args: argparse.Namespace) -> int:
        """Run tests using unittest framework"""
        self.print_info("Running tests with unittest framework...")
        
        try:
            # Build unittest command
            cmd = ['python', '-m', 'unittest']
            
            # Add discovery options
            if args.pattern:
                cmd.extend(['discover', '-s', 'tests', '-p', f'*{args.pattern}*'])
            else:
                cmd.extend(['discover', '-s', 'tests', '-p', 'test*.py'])
            
            # Add verbosity
            if args.verbose > 1:
                cmd.append('-v')
            
            # Add additional unittest args
            if args.unittest_args:
                cmd.extend(args.unittest_args.split())
            
            self.print_info(f"Running: {' '.join(cmd)}")
            
            # Run unittest
            result = subprocess.run(cmd, cwd=os.path.dirname(__file__))
            return result.returncode
            
        except Exception as e:
            self.print_error(f"Failed to run unittest: {e}")
            return 1
    
    def _run_erp_tests(self, args: argparse.Namespace) -> int:
        """Run tests using ERP testing framework"""
        try:
            from erp.tests.runner import TestRunner
            from erp.tests.tags import Tags
            
            if args.verbose > 1:
                self.print_info("Using ERP testing framework...")
            
            # Create test runner
            verbosity = 0 if args.quiet else args.verbose
            runner = TestRunner(verbosity=verbosity)
            
            # Determine test paths and modules
            test_paths = ['tests']
            addon_names = []
            
            # Handle module installation/testing
            if args.modules:
                addon_names.extend(args.modules)
            if args.update_modules:
                addon_names.extend(args.update_modules)
            if args.module:
                addon_names.append(args.module)
            
            # Discover tests
            if args.verbose > 1:
                self.print_info(f"Discovering tests in paths: {test_paths}")
                if addon_names:
                    self.print_info(f"Including addon tests: {addon_names}")
            
            runner.discover_tests(test_paths, addon_names)
            
            # Prepare tag filters
            include_tags = None
            exclude_tags = None
            
            # Handle test type filters
            if args.unit:
                include_tags = [Tags.UNIT]
            elif args.integration:
                include_tags = [Tags.INTEGRATION]
            elif args.performance:
                include_tags = [Tags.PERFORMANCE]
            elif args.async_tests:
                include_tags = ['async_test']
            
            # Handle explicit tag filters
            if args.test_tags:
                tag_list = [tag.strip() for tag in args.test_tags.split(',')]
                include_tags = tag_list if not include_tags else include_tags + tag_list
            
            if args.test_exclude_tags:
                exclude_tags = [tag.strip() for tag in args.test_exclude_tags.split(',')]
            
            # Add default exclusions for slow tests unless explicitly requested
            if not args.all_tests and not args.performance:
                if exclude_tags:
                    exclude_tags.append(Tags.SLOW)
                else:
                    exclude_tags = [Tags.SLOW]
            
            # List tests if requested
            if args.list_tests:
                test_names = runner.list_tests(include_tags, exclude_tags)
                self.print_info(f"Found {len(test_names)} tests:")
                for test_name in test_names:
                    print(f"  {test_name}")
                return 0
            
            # Run tests
            if args.verbose > 1:
                if include_tags:
                    self.print_info(f"Including tags: {include_tags}")
                if exclude_tags:
                    self.print_info(f"Excluding tags: {exclude_tags}")
            
            result = runner.run_tests(
                include_tags=include_tags,
                exclude_tags=exclude_tags,
                test_pattern=args.pattern
            )
            
            # Generate reports
            if args.coverage or args.coverage_html:
                self.print_warning("Coverage reporting not yet implemented")
            
            if args.junit_xml:
                self.print_warning(f"JUnit XML reporting to {args.junit_xml} not yet implemented")
            
            # Return appropriate exit code
            return 0 if result.wasSuccessful() else 1
            
        except ImportError as e:
            self.print_error(f"ERP testing framework not available: {e}")
            self.print_info("Falling back to pytest...")
            return self._run_pytest_tests(args)
        except Exception as e:
            self.print_error(f"Error running ERP tests: {e}")
            return 1


class TestSetupCommand(BaseCommand):
    """Setup test environment command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('--test-db', default='erp_test', help='Test database name')
        parser.add_argument('--force', action='store_true', help='Force setup even if database exists')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle test setup command"""
        try:
            self.print_info("Setting up test environment...")
            self.print_info(f"Test database: {args.test_db}")
            
            # TODO: Implement test setup
            # This would involve:
            # 1. Creating test database
            # 2. Installing base modules
            # 3. Setting up test data
            
            self.print_warning("Test setup functionality not implemented yet")
            return 0
        except Exception as e:
            self.print_error(f"Failed to setup test environment: {e}")
            return 1


class TestCleanupCommand(BaseCommand):
    """Cleanup test environment command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('--test-db', default='erp_test', help='Test database name')
        parser.add_argument('--force', action='store_true', help='Force cleanup without confirmation')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle test cleanup command"""
        try:
            if not args.force:
                response = input(f"Are you sure you want to cleanup test database '{args.test_db}'? [y/N]: ")
                if response.lower() not in ['y', 'yes']:
                    self.print_info("Operation cancelled")
                    return 0
            
            self.print_info("Cleaning up test environment...")
            self.print_info(f"Test database: {args.test_db}")
            
            # TODO: Implement test cleanup
            # This would involve:
            # 1. Dropping test database
            # 2. Cleaning up test files
            # 3. Resetting test configuration
            
            self.print_warning("Test cleanup functionality not implemented yet")
            return 0
        except Exception as e:
            self.print_error(f"Failed to cleanup test environment: {e}")
            return 1


class TestCommandGroup(CommandGroup):
    """Test command group"""
    
    def __init__(self):
        super().__init__()
        self.register_command(TestCommand())
        self.register_command(TestSetupCommand())
        self.register_command(TestCleanupCommand())
    
    def add_commands(self, subparsers):
        """Add test commands to subparsers"""
        # Main test command
        test_parser = subparsers.add_parser('test', help='Run tests')
        self.commands['test'].add_arguments(test_parser)
        
        # Test setup
        test_setup_parser = subparsers.add_parser('test-setup', help='Setup test database')
        self.commands['testsetup'].add_arguments(test_setup_parser)
        
        # Test cleanup
        test_cleanup_parser = subparsers.add_parser('test-cleanup', help='Cleanup test database')
        self.commands['testcleanup'].add_arguments(test_cleanup_parser)