"""
Domain Filter Demo - Showcasing the modular domain filtering system
"""
import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from erp.utils.domain import DomainFilter


class DemoRecord:
    """Demo record class for testing domain filtering"""
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def __repr__(self):
        return f"DemoRecord({', '.join(f'{k}={v!r}' for k, v in self.__dict__.items())})"


async def main():
    """Demonstrate domain filtering capabilities"""
    
    print("=== Domain Filter Demo ===\n")
    
    # Create sample records
    records = [
        DemoRecord(id='1', name='<PERSON>', age=28, department='Sales', active=True, salary=50000),
        DemoRecord(id='2', name='<PERSON>', age=35, department='Engineering', active=True, salary=75000),
        DemoR<PERSON>ord(id='3', name='<PERSON>', age=42, department='Sales', active=False, salary=55000),
        Demo<PERSON><PERSON>ord(id='4', name='<PERSON>', age=29, department='Marketing', active=True, salary=48000),
        DemoRecord(id='5', name='Eve Brown', age=31, department='Engineering', active=True, salary=80000),
        DemoRecord(id='6', name='<PERSON>', age=38, department='HR', active=False, salary=52000),
    ]
    
    print("Sample Records:")
    for record in records:
        print(f"  {record}")
    print()
    
    # Demo 1: Simple equality filter
    print("1. Filter by department = 'Sales':")
    domain = [('department', '=', 'Sales')]
    filtered = DomainFilter.filter_records(records, domain)
    print(f"   Domain: {DomainFilter.domain_to_string(domain)}")
    print(f"   Results: {len(filtered)} records")
    for record in filtered:
        print(f"     {record}")
    print()
    
    # Demo 2: Range filter
    print("2. Filter by age > 30:")
    domain = [('age', '>', 30)]
    filtered = DomainFilter.filter_records(records, domain)
    print(f"   Domain: {DomainFilter.domain_to_string(domain)}")
    print(f"   Results: {len(filtered)} records")
    for record in filtered:
        print(f"     {record}")
    print()
    
    # Demo 3: Multiple conditions (AND logic)
    print("3. Filter by active = True AND salary >= 60000:")
    domain = [('active', '=', True), ('salary', '>=', 60000)]
    filtered = DomainFilter.filter_records(records, domain)
    print(f"   Domain: {DomainFilter.domain_to_string(domain)}")
    print(f"   Results: {len(filtered)} records")
    for record in filtered:
        print(f"     {record}")
    print()
    
    # Demo 4: IN operator
    print("4. Filter by department in ['Engineering', 'HR']:")
    domain = [('department', 'in', ['Engineering', 'HR'])]
    filtered = DomainFilter.filter_records(records, domain)
    print(f"   Domain: {DomainFilter.domain_to_string(domain)}")
    print(f"   Results: {len(filtered)} records")
    for record in filtered:
        print(f"     {record}")
    print()
    
    # Demo 5: LIKE operator
    print("5. Filter by name like '%son' (ends with 'son'):")
    domain = [('name', 'like', '%son')]
    filtered = DomainFilter.filter_records(records, domain)
    print(f"   Domain: {DomainFilter.domain_to_string(domain)}")
    print(f"   Results: {len(filtered)} records")
    for record in filtered:
        print(f"     {record}")
    print()
    
    # Demo 6: Complex multi-condition filter
    print("6. Complex filter: active = True AND (department = 'Engineering' OR salary > 55000):")
    # Note: This demonstrates combining domains - in practice you'd use OR logic differently
    domain1 = [('active', '=', True), ('department', '=', 'Engineering')]
    domain2 = [('active', '=', True), ('salary', '>', 55000)]
    
    filtered1 = DomainFilter.filter_records(records, domain1)
    filtered2 = DomainFilter.filter_records(records, domain2)
    
    # Combine results (simulate OR logic)
    combined_ids = set()
    combined_records = []
    for record in filtered1 + filtered2:
        if record.id not in combined_ids:
            combined_ids.add(record.id)
            combined_records.append(record)
    
    print(f"   Domain 1: {DomainFilter.domain_to_string(domain1)}")
    print(f"   Domain 2: {DomainFilter.domain_to_string(domain2)}")
    print(f"   Combined Results: {len(combined_records)} records")
    for record in combined_records:
        print(f"     {record}")
    print()
    
    # Demo 7: SQL WHERE clause generation
    print("7. SQL WHERE clause generation:")
    domains_to_test = [
        [('name', '=', 'Alice')],
        [('age', '>', 25), ('active', '=', True)],
        [('department', 'in', ['Sales', 'Engineering']), ('salary', '>=', 50000)],
        [('name', 'like', '%John%'), ('active', '!=', False)]
    ]
    
    for i, domain in enumerate(domains_to_test, 1):
        where_clause, params = DomainFilter.build_sql_where_clause(domain)
        print(f"   Domain {i}: {DomainFilter.domain_to_string(domain)}")
        print(f"   SQL WHERE: {where_clause}")
        print(f"   Parameters: {params}")
        print()
    
    # Demo 8: Domain validation
    print("8. Domain validation examples:")
    valid_domains = [
        [('name', '=', 'Alice')],
        [('age', '>', 25), ('active', '=', True)],
        []
    ]
    
    invalid_domains = [
        [('name',)],  # Missing operator and value
        [('name', 'invalid_op', 'Alice')],  # Invalid operator
        [(123, '=', 'Alice')],  # Non-string field name
    ]
    
    print("   Valid domains:")
    for domain in valid_domains:
        try:
            DomainFilter.validate_domain(domain)
            print(f"     ✓ {domain}")
        except ValueError as e:
            print(f"     ✗ {domain} - {e}")
    
    print("   Invalid domains:")
    for domain in invalid_domains:
        try:
            DomainFilter.validate_domain(domain)
            print(f"     ✓ {domain}")
        except ValueError as e:
            print(f"     ✗ {domain} - {e}")
    print()
    
    print("=== Demo Complete ===")
    print("\nThe DomainFilter utility provides:")
    print("• Modular domain filtering for both database queries and in-memory records")
    print("• SQL WHERE clause generation with parameterized queries")
    print("• Support for multiple operators: =, !=, >, <, >=, <=, like, ilike, in, not in")
    print("• Domain validation and error handling")
    print("• Easy integration with BaseModel.search() and RecordSet.filtered()")
    print("• Reusable across the entire ERP application")


if __name__ == '__main__':
    asyncio.run(main())