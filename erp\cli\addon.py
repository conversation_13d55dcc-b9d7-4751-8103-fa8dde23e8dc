"""
Addon management commands
"""
import argparse
from typing import List

from .base import BaseCommand, CommandGroup
from ..addons.manager import <PERSON>don<PERSON>ana<PERSON>, AddonState


class ListCommand(BaseCommand):
    """List addons command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('--installed', action='store_true', help='Show only installed addons')
        parser.add_argument('--available', action='store_true', help='Show only available addons')
        parser.add_argument('--format', choices=['table', 'json', 'simple'], default='table', help='Output format')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle list addons command"""
        try:
            import asyncio
            manager = AddonManager()
            addons = asyncio.run(manager.discover_addons())
            
            # Filter addons based on arguments
            if args.installed:
                addons = {name: info for name, info in addons.items() 
                         if info.state == AddonState.INSTALLED}
            elif args.available:
                addons = {name: info for name, info in addons.items() 
                         if info.state == AddonState.UNINSTALLED}
            
            if args.format == 'json':
                self._print_json(addons)
            elif args.format == 'simple':
                self._print_simple(addons)
            else:
                self._print_table(addons)
            
            return 0
        except Exception as e:
            self.print_error(f"Failed to list addons: {e}")
            return 1
    
    def _print_table(self, addons: dict):
        """Print addons in table format"""
        if not addons:
            self.print_info("No addons found")
            return
        
        print(f"Found {len(addons)} addons:")
        print("-" * 70)
        
        for name, addon_info in addons.items():
            # Status icon based on state
            status_icons = {
                AddonState.INSTALLED: "✓",
                AddonState.UNINSTALLED: "○",
                AddonState.TO_INSTALL: "⏳",
                AddonState.TO_UPGRADE: "⬆",
                AddonState.TO_REMOVE: "⬇",
                AddonState.BROKEN: "✗"
            }
            
            icon = status_icons.get(addon_info.state, "?")
            state_name = addon_info.state.value.replace('_', ' ').title()
            
            # Show installed vs available version
            version_info = addon_info.available_version
            if addon_info.installed_version and addon_info.installed_version != addon_info.available_version:
                version_info = f"{addon_info.installed_version} → {addon_info.available_version}"
            
            print(f"{icon} {name} ({version_info}) - {state_name}")
            if addon_info.manifest.description:
                print(f"    {addon_info.manifest.description}")
            if addon_info.dependencies:
                print(f"    Dependencies: {', '.join(addon_info.dependencies)}")
            print()
    
    def _print_simple(self, addons: dict):
        """Print addons in simple format"""
        for name in sorted(addons.keys()):
            print(name)
    
    def _print_json(self, addons: dict):
        """Print addons in JSON format"""
        import json
        addon_data = {}
        for name, info in addons.items():
            addon_data[name] = {
                'name': info.manifest.name,
                'version': info.available_version,
                'installed_version': info.installed_version,
                'state': info.state.value,
                'description': info.manifest.description,
                'dependencies': info.dependencies,
                'dependents': info.dependents,
                'installable': info.manifest.installable,
                'auto_install': info.manifest.auto_install,
                'path': info.manifest.addon_path
            }
        print(json.dumps(addon_data, indent=2))


class InstallCommand(BaseCommand):
    """Install addon command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('addon_name', help='Addon name to install')
        parser.add_argument('--force', action='store_true', help='Force installation')
        parser.add_argument('--no-deps', action='store_true', help='Skip dependency check')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle install addon command"""
        try:
            import asyncio
            manager = AddonManager()
            asyncio.run(manager.discover_addons())
            
            self.print_info(f"Installing addon: {args.addon_name}")
            
            # Check if addon exists
            addon_info = manager.get_addon_info(args.addon_name)
            if not addon_info:
                self.print_error(f"Addon '{args.addon_name}' not found")
                return 1
            
            # Check dependencies
            if not args.force and not args.no_deps:
                deps_ok, dep_errors = manager.check_dependencies(args.addon_name)
                if not deps_ok:
                    self.print_error("Dependency issues:")
                    for error in dep_errors:
                        print(f"  - {error}")
                    print("Use --force to install anyway or --no-deps to skip dependency check")
                    return 1
            
            # Install addon
            success = asyncio.run(manager.install_addon(args.addon_name, force=args.force))
            if success:
                self.print_success(f"Addon '{args.addon_name}' installed successfully")
                return 0
            else:
                self.print_error(f"Failed to install addon '{args.addon_name}'")
                return 1
        except Exception as e:
            self.print_error(f"Failed to install addon: {e}")
            return 1


class UninstallCommand(BaseCommand):
    """Uninstall addon command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('addon_name', help='Addon name to uninstall')
        parser.add_argument('--force', action='store_true', help='Force uninstallation')
        parser.add_argument('--no-deps', action='store_true', help='Skip dependent check')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle uninstall addon command"""
        try:
            import asyncio
            manager = AddonManager()
            asyncio.run(manager.discover_addons())
            
            self.print_info(f"Uninstalling addon: {args.addon_name}")
            
            # Check if addon exists
            addon_info = manager.get_addon_info(args.addon_name)
            if not addon_info:
                self.print_error(f"Addon '{args.addon_name}' not found")
                return 1
            
            # Check dependents
            if not args.force and not args.no_deps and addon_info.dependents:
                installed_dependents = []
                for dependent in addon_info.dependents:
                    dep_info = manager.get_addon_info(dependent)
                    if dep_info and dep_info.state == AddonState.INSTALLED:
                        installed_dependents.append(dependent)
                
                if installed_dependents:
                    self.print_error(f"Cannot uninstall '{args.addon_name}': required by {', '.join(installed_dependents)}")
                    print("Use --force to uninstall anyway or --no-deps to skip dependent check")
                    return 1
            
            # Uninstall addon
            success = manager.uninstall_addon(args.addon_name, force=args.force)
            if success:
                self.print_success(f"Addon '{args.addon_name}' uninstalled successfully")
                return 0
            else:
                self.print_error(f"Failed to uninstall addon '{args.addon_name}'")
                return 1
        except Exception as e:
            self.print_error(f"Failed to uninstall addon: {e}")
            return 1


class UpgradeCommand(BaseCommand):
    """Upgrade addon command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('addon_name', nargs='?', help='Addon name to upgrade (all if not specified)')
        parser.add_argument('--all', action='store_true', help='Upgrade all addons')
        parser.add_argument('--force', action='store_true', help='Force upgrade')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle upgrade addon command"""
        try:
            import asyncio
            manager = AddonManager()
            asyncio.run(manager.discover_addons())
            
            if args.all or not args.addon_name:
                return self._upgrade_all(manager, args.force)
            else:
                return self._upgrade_single(manager, args.addon_name, args.force)
        except Exception as e:
            self.print_error(f"Failed to upgrade addon: {e}")
            return 1
    
    def _upgrade_all(self, manager: AddonManager, force: bool) -> int:
        """Upgrade all addons"""
        self.print_info("Upgrading all addons...")
        
        # Get all addons that need upgrade
        addons_to_upgrade = manager.list_addons(AddonState.TO_UPGRADE)
        
        if not addons_to_upgrade:
            self.print_info("No addons need upgrading")
            return 0
        
        success_count = 0
        for addon_name in addons_to_upgrade:
            self.print_info(f"Upgrading {addon_name}...")
            if asyncio.run(manager.upgrade_addon(addon_name, force=force)):
                success_count += 1
                self.print_success(f"Upgraded {addon_name}")
            else:
                self.print_error(f"Failed to upgrade {addon_name}")
        
        self.print_info(f"Successfully upgraded {success_count}/{len(addons_to_upgrade)} addons")
        return 0 if success_count == len(addons_to_upgrade) else 1
    
    def _upgrade_single(self, manager: AddonManager, addon_name: str, force: bool) -> int:
        """Upgrade single addon"""
        self.print_info(f"Upgrading addon: {addon_name}")
        
        # Check if addon exists
        addon_info = manager.get_addon_info(addon_name)
        if not addon_info:
            self.print_error(f"Addon '{addon_name}' not found")
            return 1
        
        # Upgrade addon
        success = asyncio.run(manager.upgrade_addon(addon_name, force=force))
        if success:
            self.print_success(f"Addon '{addon_name}' upgraded successfully")
            return 0
        else:
            self.print_error(f"Failed to upgrade addon '{addon_name}'")
            return 1


class InfoCommand(BaseCommand):
    """Show addon info command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('addon_name', help='Addon name')
        parser.add_argument('--format', choices=['table', 'json'], default='table', help='Output format')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle addon info command"""
        try:
            import asyncio
            manager = AddonManager()
            asyncio.run(manager.discover_addons())
            
            addon_info = manager.get_addon_info(args.addon_name)
            if not addon_info:
                self.print_error(f"Addon '{args.addon_name}' not found")
                return 1
            
            if args.format == 'json':
                self._print_json_info(addon_info)
            else:
                self._print_table_info(args.addon_name, addon_info, manager)
            
            return 0
        except Exception as e:
            self.print_error(f"Failed to get addon info: {e}")
            return 1
    
    def _print_table_info(self, addon_name: str, addon_info, manager: AddonManager):
        """Print addon info in table format"""
        manifest = addon_info.manifest
        print(f"Addon Information: {addon_name}")
        print("=" * 70)
        print(f"Name: {manifest.name}")
        print(f"Version: {manifest.version}")
        print(f"Author: {manifest.author}")
        print(f"Category: {manifest.category}")
        print(f"Description: {manifest.description}")
        print(f"State: {addon_info.state.value.replace('_', ' ').title()}")
        
        if addon_info.installed_version:
            print(f"Installed Version: {addon_info.installed_version}")
        if addon_info.available_version != addon_info.installed_version:
            print(f"Available Version: {addon_info.available_version}")
        if addon_info.install_date:
            print(f"Install Date: {addon_info.install_date.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"Dependencies: {', '.join(addon_info.dependencies) if addon_info.dependencies else 'None'}")
        print(f"Dependents: {', '.join(addon_info.dependents) if addon_info.dependents else 'None'}")
        print(f"Installable: {'Yes' if manifest.installable else 'No'}")
        print(f"Auto Install: {'Yes' if manifest.auto_install else 'No'}")
        print(f"Path: {manifest.addon_path}")
        
        # Show dependency tree
        print("\nDependency Tree:")
        tree = manager.get_dependency_tree(addon_name)
        self._print_dependency_tree(tree, indent=0)
        
        # Validate integrity
        is_valid, errors = manager.validate_addon_integrity(addon_name)
        print(f"\nIntegrity Check: {'✓ Valid' if is_valid else '✗ Invalid'}")
        if errors:
            for error in errors:
                print(f"  - {error}")
    
    def _print_json_info(self, addon_info):
        """Print addon info in JSON format"""
        import json
        manifest = addon_info.manifest
        data = {
            'name': manifest.name,
            'version': manifest.version,
            'author': manifest.author,
            'category': manifest.category,
            'description': manifest.description,
            'state': addon_info.state.value,
            'installed_version': addon_info.installed_version,
            'available_version': addon_info.available_version,
            'install_date': addon_info.install_date.isoformat() if addon_info.install_date else None,
            'dependencies': addon_info.dependencies,
            'dependents': addon_info.dependents,
            'installable': manifest.installable,
            'auto_install': manifest.auto_install,
            'path': manifest.addon_path
        }
        print(json.dumps(data, indent=2))
    
    def _print_dependency_tree(self, tree, indent=0):
        """Print dependency tree recursively"""
        prefix = "  " * indent
        name = tree.get('name', 'unknown')
        state = tree.get('state', 'unknown')
        version = tree.get('version', 'unknown')
        
        print(f"{prefix}- {name} ({version}) [{state}]")
        
        if tree.get('not_found'):
            print(f"{prefix}  ⚠ Not found")
        elif tree.get('max_depth_reached'):
            print(f"{prefix}  ... (max depth reached)")
        else:
            for dep in tree.get('dependencies', []):
                self._print_dependency_tree(dep, indent + 1)


class AddonCommandGroup(CommandGroup):
    """Addon command group"""
    
    def __init__(self):
        super().__init__()
        self.register_command(ListCommand())
        self.register_command(InstallCommand())
        self.register_command(UninstallCommand())
        self.register_command(UpgradeCommand())
        self.register_command(InfoCommand())
    
    def add_commands(self, subparsers):
        """Add addon commands to subparsers"""
        # List addons
        list_parser = subparsers.add_parser('list', help='List addons')
        self.commands['list'].add_arguments(list_parser)
        
        # Install addon
        install_parser = subparsers.add_parser('install', help='Install addon')
        self.commands['install'].add_arguments(install_parser)
        
        # Uninstall addon
        uninstall_parser = subparsers.add_parser('uninstall', help='Uninstall addon')
        self.commands['uninstall'].add_arguments(uninstall_parser)
        
        # Upgrade addon
        upgrade_parser = subparsers.add_parser('upgrade', help='Upgrade addon')
        self.commands['upgrade'].add_arguments(upgrade_parser)
        
        # Addon info
        info_parser = subparsers.add_parser('info', help='Show addon information')
        self.commands['info'].add_arguments(info_parser)