"""
System information and health check routes
"""
from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse, RedirectResponse

from ..config import config
from ..database.registry import DatabaseRegistry
from ..models.base import ModelRegistry
from ..utils.responses import APIResponse

router = APIRouter(tags=["system"])


@router.get("/")
async def root():
    """Root endpoint"""
    # If list_db is enabled and in multi-database mode, redirect to database list
    if config.list_db and config.is_multi_db_mode:
        return RedirectResponse(url="/app/database/list", status_code=302)
    return APIResponse.success({"message": "ERP System API", "version": "1.0.0"})


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        db = await DatabaseRegistry.get_current_database()
        if db:
            await db.execute("SELECT 1")
            db_status = "connected"
        else:
            db_status = "no_connection"
        
        return APIResponse.success({
            "status": "healthy",
            "database": db_status,
            "addons": "loaded"  # Will be updated when addon_loader is available
        })
    except Exception as e:
        return APIResponse.error(f"Health check failed: {str(e)}", status_code=503)


@router.get("/addons")
async def list_addons():
    """List all loaded addons"""
    # This will be populated by the server instance
    return APIResponse.success({})


@router.get("/models")
async def list_models():
    """List all registered models"""
    models_info = {}
    for name, model_class in ModelRegistry.all().items():
        models_info[name] = {
            "name": name,
            "description": getattr(model_class, '_description', ''),
            "table": getattr(model_class, '_table', ''),
            "fields": list(getattr(model_class, '_fields', {}).keys())
        }
    return APIResponse.success(models_info)