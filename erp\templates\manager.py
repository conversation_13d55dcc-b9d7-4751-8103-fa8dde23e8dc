"""
Template Manager - Handles template loading, caching, and resolution
"""
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
import asyncio
from .engine import Template<PERSON>ng<PERSON>
from .exceptions import TemplateNotFoundError, TemplateError


class TemplateManager:
    """Manages template loading, caching, and rendering"""
    
    def __init__(self, template_dirs: Optional[List[str]] = None):
        self.engine = TemplateEngine()
        self.template_dirs = template_dirs or []
        self.template_cache: Dict[str, str] = {}
        self._loaded_files: Dict[str, float] = {}  # filename -> mtime
        
        # Add default template directories
        self._add_default_template_dirs()
    
    def _add_default_template_dirs(self):
        """Add default template directories"""
        # Add templates directory relative to this module
        current_dir = Path(__file__).parent
        default_template_dir = current_dir / "templates"
        if default_template_dir.exists():
            self.template_dirs.append(str(default_template_dir))
        
        # Add project templates directory
        project_templates = current_dir.parent / "templates"
        if project_templates.exists():
            self.template_dirs.append(str(project_templates))
    
    def add_template_directory(self, directory: str):
        """Add a template directory to search path"""
        if directory not in self.template_dirs:
            self.template_dirs.append(directory)
    
    def load_template_file(self, filename: str) -> str:
        """Load template file content from template directories"""
        for template_dir in self.template_dirs:
            file_path = Path(template_dir) / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Cache the content and track modification time
                    self.template_cache[filename] = content
                    self._loaded_files[filename] = file_path.stat().st_mtime
                    
                    # Load templates into engine
                    self.engine.load_template(filename, content)
                    
                    return content
                except Exception as e:
                    raise TemplateError(f"Error loading template file '{filename}': {e}")
        
        raise TemplateNotFoundError(f"Template file '{filename}' not found in any template directory")
    
    def load_template_from_string(self, template_name: str, content: str):
        """Load template from string content"""
        try:
            self.engine.load_template(template_name, content)
            self.template_cache[template_name] = content
        except Exception as e:
            raise TemplateError(f"Error loading template '{template_name}': {e}")
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render a template with given context"""
        try:
            return self.engine.render_template(template_name, context)
        except Exception as e:
            raise TemplateError(f"Error rendering template '{template_name}': {e}")
    
    def template_exists(self, template_name: str) -> bool:
        """Check if a template exists"""
        return template_name in self.engine.templates
    
    def list_templates(self) -> List[str]:
        """List all loaded templates"""
        return list(self.engine.templates.keys())
    
    def reload_template_file(self, filename: str):
        """Reload a template file if it has been modified"""
        for template_dir in self.template_dirs:
            file_path = Path(template_dir) / filename
            if file_path.exists():
                current_mtime = file_path.stat().st_mtime
                cached_mtime = self._loaded_files.get(filename, 0)
                
                if current_mtime > cached_mtime:
                    # File has been modified, reload it
                    self.load_template_file(filename)
                    return True
        
        return False
    
    def auto_reload_templates(self):
        """Check and reload all modified template files"""
        reloaded = []
        for filename in list(self._loaded_files.keys()):
            if self.reload_template_file(filename):
                reloaded.append(filename)
        return reloaded
    
    def clear_cache(self):
        """Clear template cache"""
        self.template_cache.clear()
        self._loaded_files.clear()
        self.engine.templates.clear()
    
    def get_template_info(self, template_name: str) -> Dict[str, Any]:
        """Get information about a template"""
        if not self.template_exists(template_name):
            raise TemplateNotFoundError(f"Template '{template_name}' not found")
        
        return {
            'name': template_name,
            'exists': True,
            'cached': template_name in self.template_cache,
            'content_length': len(self.template_cache.get(template_name, ''))
        }


class AsyncTemplateManager(TemplateManager):
    """Async version of TemplateManager for use with FastAPI"""
    
    async def load_template_file_async(self, filename: str) -> str:
        """Async version of load_template_file"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.load_template_file, filename)
    
    async def render_template_async(self, template_name: str, context: Dict[str, Any]) -> str:
        """Async version of render_template"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.render_template, template_name, context)
    
    async def auto_reload_templates_async(self) -> List[str]:
        """Async version of auto_reload_templates"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.auto_reload_templates)


# Global template manager instance
template_manager = AsyncTemplateManager()


def get_template_manager() -> AsyncTemplateManager:
    """Get the global template manager instance"""
    return template_manager


def render_template(template_name: str, context: Dict[str, Any]) -> str:
    """Convenience function to render a template"""
    return template_manager.render_template(template_name, context)


async def render_template_async(template_name: str, context: Dict[str, Any]) -> str:
    """Convenience function to render a template asynchronously"""
    return await template_manager.render_template_async(template_name, context)
