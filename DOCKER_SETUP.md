# Docker Setup for ERP System

This document describes how to set up and run the ERP system with PostgreSQL and pgAdmin running in Docker containers while the application runs locally on the host.

## Prerequisites

- Docker and Docker Compose installed
- Python 3.8+ with virtual environment
- Git

## Quick Start

### 1. Start Database Services

```bash
# Start PostgreSQL and pgAdmin in Docker
docker-compose up -d

# Check services are running
docker-compose ps
```

### 2. Install Python Dependencies

```bash
# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

### 3. Run Application Locally

```bash
# For async server (recommended)
python erp-bin start --async-server

# For sync server
python erp-bin start
```

## Database Modes

The ERP system supports two database modes:

### Single Database Mode

When `db_name` is set in the configuration, the system operates in single database mode.

**Configuration (config/erp.conf):**
```ini
[options]
db_name = erp_db
```

**Usage:**
- All requests use the configured database
- No need to specify database in requests
- Simpler setup for single-tenant applications

### Multi-Database Mode

When `db_name` is empty or commented out, the system operates in multi-database mode.

**Configuration (config/erp-multi-db.conf):**
```ini
[options]
# db_name =  # Leave empty for multi-database mode
db_filter = ^erp_.*
```

**Usage:**
- Specify database via HTTP header: `X-Database: erp_test`
- Or via query parameter: `?db=erp_test`
- Database names must match the `db_filter` pattern
- Suitable for multi-tenant applications

## Services

### PostgreSQL (Port 5432)

- **Image:** postgres:16-alpine
- **Container:** erp_postgres
- **Default Database:** erp_db
- **Username:** erp
- **Password:** erp
- **Additional Databases:** erp_test, erp_demo

### pgAdmin (Port 8080)

- **Image:** dpage/pgadmin4:latest
- **Container:** erp_pgadmin
- **URL:** http://localhost:8080
- **Email:** <EMAIL>
- **Password:** admin

The PostgreSQL server is pre-configured in pgAdmin.

## Application Endpoints

### Status Endpoint

```bash
# Check application status
curl http://localhost:8069/

# Response includes database mode information
{
  "message": "ERP System",
  "version": "1.0.0",
  "status": "running",
  "server_type": "asgi",
  "database_mode": "single",
  "default_database": "erp_db"
}
```

### Database Listing (Multi-DB Mode)

```bash
# List available databases (only in multi-database mode)
curl http://localhost:8069/web/database/list
```

### Multi-Database Requests

```bash
# Using header
curl -H "X-Database: erp_test" http://localhost:8069/

# Using query parameter
curl http://localhost:8069/?db=erp_test
```

## Docker Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs postgres
docker-compose logs pgadmin

# Restart services
docker-compose restart

# Remove all data (destructive)
docker-compose down -v
```

## Database Management

### Connect to PostgreSQL

```bash
# Using docker exec
docker exec -it erp_postgres psql -U erp -d erp_db

# Using psql client (if installed locally)
psql -h localhost -p 5432 -U erp -d erp_db
```

### Create Additional Databases

```sql
-- Connect as erp user
CREATE DATABASE my_new_db;
GRANT ALL PRIVILEGES ON DATABASE my_new_db TO erp;
```

## Troubleshooting

### Database Connection Issues

1. Check if PostgreSQL container is running:
   ```bash
   docker-compose ps postgres
   ```

2. Check PostgreSQL logs:
   ```bash
   docker-compose logs postgres
   ```

3. Test connection:
   ```bash
   docker exec erp_postgres pg_isready -U erp
   ```

### Application Issues

1. Check configuration file path and content
2. Verify database mode settings
3. Check application logs for database connection errors
4. Ensure virtual environment is activated

### pgAdmin Access Issues

1. Check if pgAdmin container is running:
   ```bash
   docker-compose ps pgadmin
   ```

2. Access pgAdmin at http://localhost:8080
3. Use credentials: <EMAIL> / admin

## Configuration Files

- `config/erp.conf` - Single database mode configuration
- `config/erp-multi-db.conf` - Multi-database mode configuration
- `docker-compose.yml` - Docker services configuration
- `init-scripts/01-init-databases.sql` - Database initialization script
