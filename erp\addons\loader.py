"""
Addon loading and management system
"""
import os
import sys
import importlib
import importlib.util
import asyncio
from typing import Dict, List, Optional, Set, Callable, Any
from .manifest import AddonManifest
from ..config import config
from ..database.registry import DatabaseRegistry
from . import ensure_addon_import
from .hooks import get_hook_registry, HookType


class AddonLoader:
    """Addon loader and manager"""
    
    def __init__(self):
        self.addons_paths = config.addons_paths
        self.addons_path = config.addons_path  # Keep for backward compatibility
        self.loaded_addons: Dict[str, AddonManifest] = {}
        self.addon_modules: Dict[str, object] = {}
        self._dependency_graph: Dict[str, Set[str]] = {}
        self._initialization_hooks: Dict[str, List[Callable]] = {}
    
    async def discover_addons(self) -> Dict[str, AddonManifest]:
        """Discover all available addons asynchronously across all addon paths"""
        addons = {}
        loop = asyncio.get_event_loop()
        
        # Search across all addon paths
        for addons_path in self.addons_paths:
            if not await loop.run_in_executor(None, os.path.exists, addons_path):
                print(f"Addons path {addons_path} does not exist")
                continue
            
            # Run directory scanning in executor to avoid blocking
            try:
                items = await loop.run_in_executor(None, os.listdir, addons_path)
            except OSError as e:
                print(f"Error reading addons path {addons_path}: {e}")
                continue
            
            for item in items:
                addon_path = os.path.join(addons_path, item)
                
                if await loop.run_in_executor(None, os.path.isdir, addon_path):
                    # Check if it's a valid addon (has __init__.py)
                    init_file = os.path.join(addon_path, '__init__.py')
                    if await loop.run_in_executor(None, os.path.exists, init_file):
                        # Skip if addon already found in a previous path (first path wins)
                        if item not in addons:
                            manifest = AddonManifest(addon_path)
                            if manifest.installable:
                                addons[item] = manifest
                        else:
                            print(f"Addon {item} found in multiple paths, using first occurrence")
        
        return addons
    
    def _build_dependency_graph(self, addons: Dict[str, AddonManifest]):
        """Build dependency graph for addon loading order"""
        self._dependency_graph = {}
        
        for addon_name, manifest in addons.items():
            self._dependency_graph[addon_name] = set(manifest.depends)
    
    def _resolve_load_order(self, addons: Dict[str, AddonManifest]) -> List[str]:
        """Resolve addon loading order based on dependencies"""
        self._build_dependency_graph(addons)
        
        # Topological sort
        visited = set()
        temp_visited = set()
        load_order = []
        
        def visit(addon_name: str):
            if addon_name in temp_visited:
                raise ValueError(f"Circular dependency detected involving {addon_name}")
            if addon_name in visited:
                return
            
            temp_visited.add(addon_name)
            
            # Visit dependencies first
            for dep in self._dependency_graph.get(addon_name, set()):
                if dep in addons:  # Only process dependencies that are available
                    visit(dep)
            
            temp_visited.remove(addon_name)
            visited.add(addon_name)
            load_order.append(addon_name)
        
        # Visit all addons
        for addon_name in addons:
            if addon_name not in visited:
                visit(addon_name)
        
        return load_order
    
    async def load_addons(self, addon_names: Optional[List[str]] = None):
        """Load addons in dependency order"""
        # Discover available addons
        available_addons = await self.discover_addons()
        
        # Filter to requested addons if specified
        if addon_names:
            available_addons = {
                name: manifest for name, manifest in available_addons.items()
                if name in addon_names
            }
        
        # Resolve loading order
        try:
            load_order = self._resolve_load_order(available_addons)
        except ValueError as e:
            print(f"Error resolving addon dependencies: {e}")
            return
        
        # Load addons in order
        for addon_name in load_order:
            try:
                await self._load_addon(addon_name, available_addons[addon_name])
            except Exception as e:
                print(f"Error loading addon {addon_name}: {e}")
                # Continue loading other addons
                continue
    
    async def _load_addon(self, addon_name: str, manifest: AddonManifest, env: 'Environment' = None):
        """Load a single addon"""
        print(f"Loading addon: {addon_name}")

        # Get hook registry
        hook_registry = get_hook_registry()

        try:
            # Execute pre-install hooks
            await hook_registry.execute_hooks(HookType.PRE_INSTALL, addon_name, env)

            # Ensure addon import path is available
            ensure_addon_import(addon_name, manifest.path)

            # Import the addon module
            module_name = f"erp.addons.{addon_name}"

            if module_name in sys.modules:
                # Reload if already imported
                importlib.reload(sys.modules[module_name])
                addon_module = sys.modules[module_name]
            else:
                addon_module = importlib.import_module(module_name)

            # Store the loaded module
            self.addon_modules[addon_name] = addon_module
            self.loaded_addons[addon_name] = manifest

            # Call initialization hooks if present
            if hasattr(addon_module, 'initialize'):
                if asyncio.iscoroutinefunction(addon_module.initialize):
                    await addon_module.initialize()
                else:
                    addon_module.initialize()

            # Register any initialization hooks for later use
            if hasattr(addon_module, 'post_init_hook'):
                if addon_name not in self._initialization_hooks:
                    self._initialization_hooks[addon_name] = []
                self._initialization_hooks[addon_name].append(addon_module.post_init_hook)

            # Execute post-install hooks
            await hook_registry.execute_hooks(HookType.POST_INSTALL, addon_name, env)

            print(f"Successfully loaded addon: {addon_name}")

        except Exception as e:
            print(f"Failed to load addon {addon_name}: {e}")
            raise
    
    async def unload_addon(self, addon_name: str, env: 'Environment' = None):
        """Unload an addon"""
        if addon_name not in self.loaded_addons:
            print(f"Addon {addon_name} is not loaded")
            return

        # Get hook registry
        hook_registry = get_hook_registry()

        try:
            # Execute pre-uninstall hooks
            await hook_registry.execute_hooks(HookType.PRE_UNINSTALL, addon_name, env)

            # Call cleanup hooks if present
            addon_module = self.addon_modules.get(addon_name)
            if addon_module and hasattr(addon_module, 'cleanup'):
                if asyncio.iscoroutinefunction(addon_module.cleanup):
                    await addon_module.cleanup()
                else:
                    addon_module.cleanup()

            # Remove from loaded addons
            del self.loaded_addons[addon_name]
            if addon_name in self.addon_modules:
                del self.addon_modules[addon_name]
            if addon_name in self._initialization_hooks:
                del self._initialization_hooks[addon_name]

            # Remove from sys.modules
            module_name = f"erp.addons.{addon_name}"
            if module_name in sys.modules:
                del sys.modules[module_name]

            # Unregister hooks for this addon
            hook_registry.unregister_addon_hooks(addon_name)

            # Execute post-uninstall hooks
            await hook_registry.execute_hooks(HookType.POST_UNINSTALL, addon_name, env)

            print(f"Successfully unloaded addon: {addon_name}")

        except Exception as e:
            print(f"Error unloading addon {addon_name}: {e}")
            raise
    
    async def reload_addon(self, addon_name: str, env: 'Environment' = None):
        """Reload an addon"""
        if addon_name in self.loaded_addons:
            await self.unload_addon(addon_name, env)

        # Rediscover to get updated manifest
        available_addons = await self.discover_addons()
        if addon_name in available_addons:
            await self._load_addon(addon_name, available_addons[addon_name], env)
        else:
            print(f"Addon {addon_name} not found for reload")

    async def upgrade_addon(self, addon_name: str, env: 'Environment' = None):
        """Upgrade an addon"""
        if addon_name not in self.loaded_addons:
            print(f"Addon {addon_name} is not loaded")
            return

        # Get hook registry
        hook_registry = get_hook_registry()

        try:
            # Execute pre-upgrade hooks
            await hook_registry.execute_hooks(HookType.PRE_UPGRADE, addon_name, env)

            # Reload the addon (which will unload and load again)
            await self.reload_addon(addon_name, env)

            # Execute post-upgrade hooks
            await hook_registry.execute_hooks(HookType.POST_UPGRADE, addon_name, env)

            print(f"Successfully upgraded addon: {addon_name}")

        except Exception as e:
            print(f"Error upgrading addon {addon_name}: {e}")
            raise
    
    def get_loaded_addon(self, addon_name: str) -> Optional[AddonManifest]:
        """Get manifest for a loaded addon"""
        return self.loaded_addons.get(addon_name)
    
    def get_addon_module(self, addon_name: str) -> Optional[object]:
        """Get the Python module for a loaded addon"""
        return self.addon_modules.get(addon_name)
    
    def is_addon_loaded(self, addon_name: str) -> bool:
        """Check if an addon is loaded"""
        return addon_name in self.loaded_addons
    
    async def run_post_init_hooks(self):
        """Run all post-initialization hooks"""
        for addon_name, hooks in self._initialization_hooks.items():
            for hook in hooks:
                try:
                    if asyncio.iscoroutinefunction(hook):
                        await hook()
                    else:
                        hook()
                except Exception as e:
                    print(f"Error running post-init hook for {addon_name}: {e}")
    
    def get_addon_info(self, addon_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a loaded addon"""
        if addon_name not in self.loaded_addons:
            return None
        
        manifest = self.loaded_addons[addon_name]
        return {
            "name": manifest.name,
            "version": manifest.version,
            "description": manifest.description,
            "author": manifest.author,
            "depends": manifest.depends,
            "installable": manifest.installable,
            "auto_install": manifest.auto_install,
            "path": manifest.path
        }
    
    def list_loaded_addons(self) -> List[str]:
        """Get list of all loaded addon names"""
        return list(self.loaded_addons.keys())
