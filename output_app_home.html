<html>
            <head>
                <title>ERP System Dashboard</title><meta charset="utf-8"></meta>
                <meta name="viewport" content="width=device-width, initial-scale=1"></meta>
                <style>
                    body {
                        font-family: &#x27;Segoe UI&#x27;, Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 0;
                        background-color: #f5f5f5;
                    }
                    .header {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 20px;
                        text-align: center;
                    }
                    .container {
                        max-width: 1200px;
                        margin: 20px auto;
                        padding: 0 20px;
                    }
                    .card {
                        background: white;
                        border-radius: 8px;
                        padding: 20px;
                        margin: 20px 0;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    .status-info {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                        gap: 20px;
                        margin: 20px 0;
                    }
                    .status-card {
                        background: white;
                        border-radius: 8px;
                        padding: 20px;
                        text-align: center;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    .status-value {
                        font-size: 2em;
                        font-weight: bold;
                        color: #667eea;
                        margin: 10px 0;
                    }
                    .status-label {
                        color: #666;
                        font-size: 0.9em;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>ERP System Dashboard</h1><p>Welcome to your ERP system</p></div>
                
                <div class="container">
                    
                    <div class="card">
                        <h2>Database Information</h2>
                        <p><strong>Database:</strong> <span>demo_db</span></p>
                        <p><strong>Mode:</strong> <span>multi-database</span></p>
                    </div>
                    
                    
                    <div class="card">
                        <h2>System Status</h2>
                        <div class="status-info">
                            <div class="status-card">
                                <div class="status-value">5</div><div class="status-label">Loaded Addons</div>
                            </div>
                            <div class="status-card">
                                <div class="status-value">✓</div><div class="status-label">Database Connection</div>
                            </div>
                            <div class="status-card">
                                <div class="status-value">Running</div><div class="status-label">Server Status</div>
                            </div>
                        </div>
                    </div>
                    
                    
                    <div class="card">
                        <h2>Quick Actions</h2>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <button onclick="window.location.href=&#x27;/api/databases&#x27;" style="padding: 15px; border: none; background: #667eea; color: white; border-radius: 4px; cursor: pointer;">
                                View Databases
                            </button>
                            <button onclick="window.location.href=&#x27;/addons&#x27;" style="padding: 15px; border: none; background: #764ba2; color: white; border-radius: 4px; cursor: pointer;">
                                View Addons
                            </button>
                            <button onclick="window.location.href=&#x27;/health&#x27;" style="padding: 15px; border: none; background: #28a745; color: white; border-radius: 4px; cursor: pointer;">
                                Health Check
                            </button>
                        </div>
                    </div>
                    
                    
                    <div style="text-align: center; margin: 40px 0; color: #666;">
                        <small>
                            ERP System v1.0 - Powered by FastAPI and Custom Template Engine
                        </small>
                    </div>
                </div>
            </body>
        </html>
    