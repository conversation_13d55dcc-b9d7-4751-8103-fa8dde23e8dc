<html>
            <head>
                <title>Template Engine Demo</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                    .highlight { background-color: #ffffcc; }
                    .error { color: red; }
                    .success { color: green; }
                    table { border-collapse: collapse; width: 100%; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; }
                </style>
            </head>
            <body>
                <h1>Template Engine Demo</h1>
                
                
                <div class="section">
                    <h2>Variables and Expressions</h2>
                    <p>Company: <strong>Demo Company Inc.</strong></p>
                    <p>Year: <span>2024</span></p>
                    <p>Next Year: <span>2025</span></p>
                </div>
                
                
                <div class="section">
                    <h2>Conditional Rendering</h2>
                    <p class="success">✓ Admin access granted</p>
                    <p class="error">✗ Access denied</p>
                    <p>Good performance</p>
                    <p class="error">Needs improvement</p>
                    </div>
                
                
                <div class="section">
                    <h2>Dynamic Attributes</h2>
                    <div class="highlight">
                        This div has dynamic class based on importance
                    
                    
                    </div><p style="color: blue;">
                        This text color depends on item count (<span>5</span>
                </p></div>
                
                
                <div class="section">
                    <h2>Loops and Iteration</h2>
                    <h3>Product List:</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Product Name</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="">
                                    <td>1</td><td>Product A</td><td>$<span>100</span></td>
                                    <td>5</td><td>
                                        <span class="success">In Stock</span></td>
                                
                            </tr><tr class="error">
                                    <td>2</td><td>Product B</td><td>$<span>200</span></td>
                                    <td>0</td><td>
                                        <span class="error">Out of Stock</span></td>
                                
                            </tr><tr class="">
                                    <td>3</td><td>Product C</td><td>$<span>150</span></td>
                                    <td>10</td><td>
                                        <span class="success">In Stock</span></td>
                                
                            </tr></tbody>
                    </table>
                </div>
                
                
                <div class="section">
                    <h2>Raw HTML vs Escaped Content</h2>
                    <p>Escaped content: <span>&lt;strong&gt;Bold Text&lt;/strong&gt; &amp; special chars</span></p>
                    <p>Raw content: <span><em>Italic Text</em></span></p>
                </div>
                
                
                <div class="section">
                    <h2>Nested Loops</h2>
                    <h4>Electronics</h4><ul>
                            <li>Phone</li><li>Laptop</li></ul>
                    <h4>Books</h4><ul>
                            <li>Novel</li><li>Textbook</li><li>Magazine</li></ul>
                    </div>
                
                
                <div style="margin-top: 40px; text-align: center; color: #666; border-top: 1px solid #ddd; padding-top: 20px;">
                    <small>Generated by ERP Template Engine</small>
                </div>
            </body>
        </html>
    