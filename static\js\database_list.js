// Database list functionality
class DatabaseManager {
    constructor() {
        this.databases = [];
        this.selectedDatabase = null;
        this.init();
    }

    async init() {
        await this.loadDatabases();
        this.setupEventListeners();
    }

    async loadDatabases() {
        try {
            this.showLoading();
            const response = await fetch('/api/databases');
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.databases = data.data;
                this.renderDatabases();
            } else {
                throw new Error(data.message || 'Failed to load databases');
            }
        } catch (error) {
            console.error('Error loading databases:', error);
            this.showError('Failed to load databases: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    renderDatabases() {
        const grid = document.getElementById('databaseGrid');
        
        if (this.databases.length === 0) {
            grid.innerHTML = `
                <div class="no-databases">
                    <i class="fas fa-database" style="font-size: 3rem; color: #ccc; margin-bottom: 16px;"></i>
                    <h3>No databases found</h3>
                    <p>Create a new database to get started.</p>
                </div>
            `;
            return;
        }

        grid.innerHTML = this.databases.map(db => this.createDatabaseCard(db)).join('');
        
        // Highlight current database after rendering
        this.highlightCurrentDatabase();
    }

    createDatabaseCard(db) {
        const statusClass = db.status === 'active' ? 'status-active' : 'status-inactive';
        const sizeFormatted = this.formatSize(db.size);
        const createdFormatted = this.formatDate(db.created);
        
        return `
            <div class="database-card" onclick="databaseManager.selectDatabase('${db.name}')" data-db="${db.name}">
                <div class="database-header">
                    <div class="database-name">
                        <i class="fas fa-database"></i>
                        ${db.name}
                    </div>
                    <div class="database-status ${statusClass}">
                        ${db.status}
                    </div>
                </div>
                
                <div class="database-info">
                    <div class="info-item">
                        <div class="info-label">Size</div>
                        <div class="info-value">${sizeFormatted}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Created</div>
                        <div class="info-value">${createdFormatted}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Owner</div>
                        <div class="info-value">${db.owner || 'erp'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Encoding</div>
                        <div class="info-value">${db.encoding || 'UTF8'}</div>
                    </div>
                </div>
                
                <div class="database-actions">
                    <button class="btn btn-primary btn-small" onclick="event.stopPropagation(); databaseManager.connectToDatabase('${db.name}')">
                        <i class="fas fa-sign-in-alt"></i>
                        Connect
                    </button>
                    <button class="btn btn-secondary btn-small" onclick="event.stopPropagation(); databaseManager.showDatabaseInfo('${db.name}')">
                        <i class="fas fa-info-circle"></i>
                        Info
                    </button>
                    <button class="btn btn-danger btn-small" onclick="event.stopPropagation(); databaseManager.confirmDeleteDatabase('${db.name}')">
                        <i class="fas fa-trash"></i>
                        Delete
                    </button>
                </div>
            </div>
        `;
    }

    selectDatabase(dbName) {
        // Remove previous selection
        document.querySelectorAll('.database-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Add selection to clicked card
        const card = document.querySelector(`[data-db="${dbName}"]`);
        if (card) {
            card.classList.add('selected');
            this.selectedDatabase = dbName;
        }
    }

    async connectToDatabase(dbName) {
        try {
            this.showLoading();

            // Redirect to the home page with database parameter
            // This will trigger cookie setting via middleware
            window.location.href = `/app?db=${dbName}`;

        } catch (error) {
            console.error('Error connecting to database:', error);
            this.showError('Failed to connect to database: ' + error.message);
            this.hideLoading();
        }
    }

    getCurrentDatabase() {
        // Get current database from cookie
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'erp_database') {
                return decodeURIComponent(value);
            }
        }
        return null;
    }

    highlightCurrentDatabase() {
        // Highlight the currently selected database from cookie
        const currentDb = this.getCurrentDatabase();
        if (currentDb) {
            const card = document.querySelector(`[data-db="${currentDb}"]`);
            if (card) {
                card.classList.add('current-database');
                // Add a visual indicator
                const indicator = document.createElement('div');
                indicator.className = 'current-indicator';
                indicator.innerHTML = '<i class="fas fa-check-circle"></i> Current';
                card.querySelector('.database-header').appendChild(indicator);
            }
        }
    }

    async createDatabase(formData) {
        try {
            this.showLoading();
            
            const response = await fetch('/api/databases', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.hideCreateDatabase();
                await this.loadDatabases();
                this.showSuccess('Database created successfully!');
            } else {
                throw new Error(data.message || 'Failed to create database');
            }
        } catch (error) {
            console.error('Error creating database:', error);
            this.showError('Failed to create database: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async deleteDatabase(dbName) {
        try {
            this.showLoading();
            
            const response = await fetch(`/api/databases/${dbName}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                await this.loadDatabases();
                this.showSuccess('Database deleted successfully!');
            } else {
                throw new Error(data.message || 'Failed to delete database');
            }
        } catch (error) {
            console.error('Error deleting database:', error);
            this.showError('Failed to delete database: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    confirmDeleteDatabase(dbName) {
        if (confirm(`Are you sure you want to delete the database "${dbName}"? This action cannot be undone.`)) {
            this.deleteDatabase(dbName);
        }
    }

    showDatabaseInfo(dbName) {
        const db = this.databases.find(d => d.name === dbName);
        if (db) {
            alert(`Database Information:\n\nName: ${db.name}\nSize: ${this.formatSize(db.size)}\nCreated: ${this.formatDate(db.created)}\nOwner: ${db.owner || 'erp'}\nEncoding: ${db.encoding || 'UTF8'}\nStatus: ${db.status}`);
        }
    }

    setupEventListeners() {
        // Create database form
        const form = document.getElementById('createDbForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                const formData = new FormData(form);
                const data = {
                    name: formData.get('dbName'),
                    language: formData.get('dbLanguage'),
                    demo: formData.get('loadDemo') === 'on'
                };
                this.createDatabase(data);
            });
        }
    }

    // UI Helper methods
    showLoading() {
        document.getElementById('loading').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        errorText.textContent = message;
        errorDiv.style.display = 'flex';
        
        // Auto-hide after 5 seconds
        setTimeout(() => this.hideError(), 5000);
    }

    hideError() {
        document.getElementById('errorMessage').style.display = 'none';
    }

    showSuccess(message) {
        // For now, use alert. Could be enhanced with a proper success notification
        alert(message);
    }

    // Utility methods
    formatSize(sizeStr) {
        if (!sizeStr) return 'Unknown';
        return sizeStr;
    }

    formatDate(dateStr) {
        if (!dateStr) return 'Unknown';
        try {
            const date = new Date(dateStr);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        } catch {
            return dateStr;
        }
    }
}

// Global functions for HTML onclick handlers
function refreshDatabases() {
    databaseManager.loadDatabases();
}

function showCreateDatabase() {
    document.getElementById('createDbModal').style.display = 'flex';
}

function hideCreateDatabase() {
    document.getElementById('createDbModal').style.display = 'none';
    document.getElementById('createDbForm').reset();
}

function hideError() {
    databaseManager.hideError();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.databaseManager = new DatabaseManager();
});
