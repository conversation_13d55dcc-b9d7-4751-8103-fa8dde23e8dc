# HookContext Typing Improvements

## Overview
Enhanced the typing system for the `HookContext` class and related components in `erp/addons/hooks.py` to provide better debugging support and IDE assistance.

## Changes Made

### 1. HookContext Class
**Before:**
```python
class HookContext:
    def __init__(self, addon_name: str, hook_type: HookType, env: 'Environment' = None, **kwargs):
        self.addon_name = addon_name
        self.hook_type = hook_type
        self.env = env
        self.data = kwargs
```

**After:**
```python
class HookContext:
    # Type annotations for instance attributes
    addon_name: str
    hook_type: HookType
    env: Optional['Environment']
    data: Dict[str, Any]
    
    def __init__(
        self, 
        addon_name: str, 
        hook_type: HookType, 
        env: Optional['Environment'] = None,
        **kwargs: Any
    ) -> None:
        self.addon_name = addon_name
        self.hook_type = hook_type
        self.env = env
        self.data = kwargs
    
    def __repr__(self) -> str:
        return f"HookContext(addon={self.addon_name}, type={self.hook_type.value})"
```

### 2. AddonHook Class
**Improvements:**
- Added explicit type annotations for all instance attributes
- Improved function signature typing with `Callable[[HookContext], Any]`
- Added return type annotations for all methods

### 3. HookRegistry Class
**Improvements:**
- Added type annotations for instance attributes
- Improved method signatures with proper parameter and return types
- Enhanced type safety for hook registration and execution

### 4. Decorator Functions
**Improvements:**
- Added comprehensive type annotations for all decorator functions
- Proper return type specifications: `Callable[[Callable[[HookContext], Any]], Callable[[HookContext], Any]]`
- Enhanced parameter typing with `Optional[str]` for addon_name

### 5. Import Structure
**Improvements:**
- Added `TYPE_CHECKING` import for proper forward reference handling
- Implemented runtime-safe Environment type handling
- Added proper conditional imports to avoid circular dependencies

## Benefits

### 1. Better IDE Support
- **Autocomplete**: IDEs can now provide accurate autocomplete suggestions for HookContext properties
- **Type Hints**: Developers get real-time type information while coding
- **Error Detection**: IDEs can catch type-related errors before runtime

### 2. Enhanced Debugging
- **Property Types**: All HookContext properties now have explicit types:
  - `addon_name: str` - The name of the addon
  - `hook_type: HookType` - The type of lifecycle hook
  - `env: Optional[Environment]` - The environment instance (can be None)
  - `data: Dict[str, Any]` - Additional data passed via **kwargs

### 3. Type Safety
- **Runtime Validation**: Type annotations help catch type mismatches early
- **Documentation**: Types serve as inline documentation for expected data types
- **Maintainability**: Easier to understand and maintain code with explicit types

## Usage Examples

### Creating a HookContext with proper typing:
```python
from erp.addons.hooks import HookContext, HookType

# All properties are now properly typed
context = HookContext(
    addon_name="my_addon",
    hook_type=HookType.PRE_INSTALL,
    env=None,  # Optional[Environment]
    custom_data="example",  # Goes into data: Dict[str, Any]
    debug_mode=True
)

# IDE will provide autocomplete and type checking
print(context.addon_name)  # str
print(context.hook_type)   # HookType
print(context.env)         # Optional[Environment]
print(context.data)        # Dict[str, Any]
```

### Using decorators with proper typing:
```python
@pre_install_hook("my_addon")
def my_hook(context: HookContext) -> str:
    # IDE knows context.env is Optional[Environment]
    if context.env:
        db_name = context.env.cr.db_name  # Properly typed
        user_id = context.env.uid         # int
        ctx_data = context.env.context    # Dict[str, Any]
    
    # context.data is Dict[str, Any]
    custom_value = context.data.get("custom_data")
    
    return "hook_completed"
```

## Testing
All typing improvements have been tested to ensure:
- ✅ Code compiles without errors
- ✅ Runtime functionality remains unchanged
- ✅ Type annotations work correctly
- ✅ Decorator functions maintain their behavior
- ✅ Forward references are handled properly

## Compatibility
- **Backward Compatible**: All existing code continues to work without changes
- **Python 3.7+**: Uses modern typing features with proper fallbacks
- **IDE Support**: Enhanced support for PyCharm, VSCode, and other Python IDEs
- **Type Checkers**: Compatible with mypy, pyright, and other static type checkers