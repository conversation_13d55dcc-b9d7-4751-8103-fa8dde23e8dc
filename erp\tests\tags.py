"""
Test tagging system for ERP tests
Provides decorators for test categorization and filtering
"""
import functools
from typing import Set, Union, List


def tag(*tags: str):
    """
    Decorator to tag test methods or classes
    
    Usage:
        @tag('slow', 'integration')
        def test_something(self):
            pass
    """
    def decorator(obj):
        if not hasattr(obj, '_test_tags'):
            obj._test_tags = set()
        obj._test_tags.update(tags)
        return obj
    return decorator


def tagged(*tags: str):
    """
    Alias for tag decorator for compatibility
    """
    return tag(*tags)


def get_test_tags(test_obj) -> Set[str]:
    """Get tags from a test object (method or class)"""
    tags = set()
    
    # Get tags from the test method
    if hasattr(test_obj, '_test_tags'):
        tags.update(test_obj._test_tags)
    
    # Get tags from the test class
    if hasattr(test_obj, '__self__') and hasattr(test_obj.__self__.__class__, '_test_tags'):
        tags.update(test_obj.__self__.__class__._test_tags)
    elif hasattr(test_obj, '__class__') and hasattr(test_obj.__class__, '_test_tags'):
        tags.update(test_obj.__class__._test_tags)
    
    return tags


def should_run_test(test_obj, include_tags: Set[str] = None, exclude_tags: Set[str] = None) -> bool:
    """
    Determine if a test should run based on its tags
    
    Args:
        test_obj: Test method or class
        include_tags: Tags that must be present (any of them)
        exclude_tags: Tags that must not be present (none of them)
    
    Returns:
        True if test should run, False otherwise
    """
    test_tags = get_test_tags(test_obj)
    
    # If exclude tags are specified and test has any of them, skip
    if exclude_tags and test_tags.intersection(exclude_tags):
        return False
    
    # If include tags are specified and test doesn't have any of them, skip
    if include_tags and not test_tags.intersection(include_tags):
        return False
    
    return True


def filter_tests_by_tags(tests: List, include_tags: Union[str, List[str]] = None, 
                        exclude_tags: Union[str, List[str]] = None) -> List:
    """
    Filter a list of tests based on tags
    
    Args:
        tests: List of test objects
        include_tags: Tags to include (string or list of strings)
        exclude_tags: Tags to exclude (string or list of strings)
    
    Returns:
        Filtered list of tests
    """
    # Convert string tags to sets
    if isinstance(include_tags, str):
        include_tags = {include_tags}
    elif isinstance(include_tags, list):
        include_tags = set(include_tags)
    
    if isinstance(exclude_tags, str):
        exclude_tags = {exclude_tags}
    elif isinstance(exclude_tags, list):
        exclude_tags = set(exclude_tags)
    
    filtered_tests = []
    for test in tests:
        if should_run_test(test, include_tags, exclude_tags):
            filtered_tests.append(test)
    
    return filtered_tests


# Common test tags
class Tags:
    """Common test tags for consistency"""
    UNIT = 'unit'
    INTEGRATION = 'integration'
    PERFORMANCE = 'performance'
    SLOW = 'slow'
    FAST = 'fast'
    DATABASE = 'database'
    NETWORK = 'network'
    SECURITY = 'security'
    UI = 'ui'
    API = 'api'
    MIGRATION = 'migration'
    SMOKE = 'smoke'
    REGRESSION = 'regression'
    NIGHTLY = 'nightly'
    
    # Addon-specific tags
    BASE = 'base'
    SALE = 'sale'
    PURCHASE = 'purchase'
    INVENTORY = 'inventory'
    ACCOUNTING = 'accounting'
    HR = 'hr'
    CRM = 'crm'
    
    @classmethod
    def all_tags(cls) -> Set[str]:
        """Get all defined tags"""
        return {
            value for name, value in cls.__dict__.items() 
            if not name.startswith('_') and isinstance(value, str)
        }
