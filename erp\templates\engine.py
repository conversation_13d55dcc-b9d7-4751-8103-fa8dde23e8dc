"""
Template Engine - Core rendering functionality
"""
import xml.etree.ElementTree as ET
from typing import Dict, Any, List, Optional, Union
import html
import re
from .parser import TemplateParser, ExpressionEvaluator, LoopContext
from .exceptions import TemplateRenderError, TemplateSyntaxError


class TemplateEngine:
    """Core template rendering engine"""
    
    def __init__(self):
        self.parser = TemplateParser()
        self.evaluator = ExpressionEvaluator()
        self.templates: Dict[str, ET.Element] = {}
    
    def load_template(self, template_name: str, template_content: str):
        """Load a template from content"""
        templates = self.parser.parse_template_file(template_content)
        self.templates.update(templates)
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render a template with given context"""
        if template_name not in self.templates:
            raise TemplateRenderError(f"Template '{template_name}' not found")
        
        template_element = self.templates[template_name]
        return self._render_element(template_element, context)
    
    def _render_element(self, element: ET.Element, context: Dict[str, Any]) -> str:
        """Render a single XML element"""
        # Handle template control elements (t tags)
        if self.parser.is_template_element(element):
            return self._render_template_element(element, context)
        
        # Handle regular elements with directives
        if self.parser.has_directives(element):
            return self._render_element_with_directives(element, context)
        
        # Handle regular elements without directives
        return self._render_regular_element(element, context)
    
    def _render_template_element(self, element: ET.Element, context: Dict[str, Any]) -> str:
        """Render template control element (t tag)"""
        directives = self.parser.extract_directives(element)

        # Handle t-ignore first
        if 't-ignore' in directives:
            ignore_value = directives['t-ignore']
            if ignore_value.lower() in ('true', '1') or self.evaluator.evaluate_condition(ignore_value, context):
                return ""

        # Handle conditional rendering
        if not self._should_render_conditionally(directives, context):
            return ""

        # Handle t-foreach
        if 't-foreach' in directives:
            return self._render_foreach(element, directives, context)

        # Handle t-set
        if 't-set' in directives:
            return self._render_set(element, directives, context)

        # Handle content directives
        if 't-esc' in directives:
            value = self.evaluator.evaluate(directives['t-esc'], context)
            return html.escape(str(value))

        if 't-raw' in directives:
            value = self.evaluator.evaluate(directives['t-raw'], context)
            return str(value)

        if 't-field' in directives:
            value = self.evaluator.evaluate(directives['t-field'], context)
            return html.escape(str(value))

        # Render children
        return self._render_children(element, context)

    def _should_render_conditionally(self, directives: Dict[str, str], context: Dict[str, Any]) -> bool:
        """Check if element should be rendered based on conditional directives"""
        if 't-if' in directives:
            condition = directives['t-if']
            return self.evaluator.evaluate_condition(condition, context)
        elif 't-elif' in directives:
            condition = directives['t-elif']
            return self.evaluator.evaluate_condition(condition, context)
        elif 't-else' in directives:
            # t-else is always true when reached
            return True

        return True  # No conditional directive, render by default
    
    def _render_element_with_directives(self, element: ET.Element, context: Dict[str, Any]) -> str:
        """Render regular element that has template directives"""
        directives = self.parser.extract_directives(element)

        # Handle t-ignore first
        if 't-ignore' in directives:
            ignore_value = directives['t-ignore']
            if ignore_value.lower() in ('true', '1') or self.evaluator.evaluate_condition(ignore_value, context):
                return ""

        # Handle conditional rendering
        if not self._should_render_conditionally(directives, context):
            return ""

        # Handle t-foreach on regular elements
        if 't-foreach' in directives:
            return self._render_foreach_element(element, directives, context)

        # Start building element
        tag_name = element.tag
        attributes = self._process_element_attributes(element, directives, context)

        # Build opening tag
        attr_str = ' '.join(f'{name}="{html.escape(value)}"' for name, value in attributes.items())
        opening_tag = f"<{tag_name}" + (f" {attr_str}" if attr_str else "") + ">"

        # Handle content
        content = self._render_element_content(element, directives, context)

        # Build closing tag
        closing_tag = f"</{tag_name}>"

        return opening_tag + content + closing_tag

    def _process_element_attributes(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> Dict[str, str]:
        """Process element attributes including dynamic ones"""
        attributes = {}

        for attr_name, attr_value in element.attrib.items():
            if attr_name.startswith('t-'):
                # Handle template directives
                if attr_name.startswith('t-att-'):
                    # Dynamic attribute
                    real_attr_name = attr_name[6:]  # Remove 't-att-' prefix
                    try:
                        evaluated_value = self.evaluator.evaluate(attr_value, context)
                        attributes[real_attr_name] = str(evaluated_value)
                    except Exception as e:
                        raise TemplateRenderError(f"Error evaluating t-att-{real_attr_name}: {e}")
                elif attr_name == 't-att-class':
                    # Special handling for class attribute
                    try:
                        evaluated_value = self.evaluator.evaluate(attr_value, context)
                        attributes['class'] = str(evaluated_value)
                    except Exception as e:
                        raise TemplateRenderError(f"Error evaluating t-att-class: {e}")
                elif attr_name == 't-att-style':
                    # Special handling for style attribute
                    try:
                        evaluated_value = self.evaluator.evaluate(attr_value, context)
                        attributes['style'] = str(evaluated_value)
                    except Exception as e:
                        raise TemplateRenderError(f"Error evaluating t-att-style: {e}")
                # Skip other directives (they're handled elsewhere)
            else:
                # Regular attribute
                attributes[attr_name] = attr_value

        return attributes

    def _render_element_content(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Render element content based on directives"""
        # Handle t-esc (escaped content)
        if 't-esc' in directives:
            value = self.evaluator.evaluate(directives['t-esc'], context)
            return html.escape(str(value))

        # Handle t-raw (raw HTML content)
        elif 't-raw' in directives:
            value = self.evaluator.evaluate(directives['t-raw'], context)
            return str(value)

        # Handle t-field (field content - simplified)
        elif 't-field' in directives:
            value = self.evaluator.evaluate(directives['t-field'], context)
            return html.escape(str(value))

        else:
            # Render children and text content
            content = ""
            if element.text:
                content += html.escape(element.text)
            content += self._render_children(element, context)
            if element.tail:
                content += html.escape(element.tail)
            return content
    
    def _render_regular_element(self, element: ET.Element, context: Dict[str, Any]) -> str:
        """Render regular element without directives"""
        tag_name = element.tag
        
        # Build attributes
        attr_str = ' '.join(f'{name}="{html.escape(value)}"' for name, value in element.attrib.items())
        opening_tag = f"<{tag_name}" + (f" {attr_str}" if attr_str else "") + ">"
        
        # Build content
        content = ""
        if element.text:
            content += html.escape(element.text)
        content += self._render_children(element, context)
        
        closing_tag = f"</{tag_name}>"
        
        result = opening_tag + content + closing_tag
        
        # Add tail text if present
        if element.tail:
            result += html.escape(element.tail)
        
        return result
    
    def _render_children(self, element: ET.Element, context: Dict[str, Any]) -> str:
        """Render all child elements"""
        result = ""
        for child in element:
            result += self._render_element(child, context)
        return result

    def _render_foreach_element(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Render t-foreach on a regular element (not t tag)"""
        foreach_expr = directives['t-foreach']
        as_var = directives.get('t-as', 'item')

        # Evaluate the iterable
        try:
            items = self.evaluator.evaluate(foreach_expr, context)
            if not hasattr(items, '__iter__'):
                items = [items]
        except Exception as e:
            raise TemplateRenderError(f"Error in t-foreach: {e}")

        # Render loop - each iteration renders the entire element
        result = ""
        loop_context = LoopContext(list(items), as_var)

        for i, item in enumerate(items):
            loop_context.index = i

            # Create new context with loop variables
            loop_ctx = context.copy()
            loop_ctx[as_var] = item
            loop_ctx.update(loop_context.loop_vars)

            # Create a copy of the element without the t-foreach directive
            elem_copy = ET.Element(element.tag, element.attrib.copy())
            elem_copy.text = element.text
            elem_copy.tail = element.tail
            for child in element:
                elem_copy.append(child)

            # Remove t-foreach and t-as from the copy
            if 't-foreach' in elem_copy.attrib:
                del elem_copy.attrib['t-foreach']
            if 't-as' in elem_copy.attrib:
                del elem_copy.attrib['t-as']

            # Render the element for this iteration
            result += self._render_element(elem_copy, loop_ctx)

        return result

    def _render_foreach(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Render t-foreach loop"""
        foreach_expr = directives['t-foreach']
        as_var = directives.get('t-as', 'item')
        
        # Evaluate the iterable
        try:
            items = self.evaluator.evaluate(foreach_expr, context)
            if not hasattr(items, '__iter__'):
                items = [items]
        except Exception as e:
            raise TemplateRenderError(f"Error in t-foreach: {e}")
        
        # Render loop
        result = ""
        loop_context = LoopContext(list(items), as_var)
        
        for i, item in enumerate(items):
            loop_context.index = i
            
            # Create new context with loop variables
            loop_ctx = context.copy()
            loop_ctx[as_var] = item
            loop_ctx.update(loop_context.loop_vars)
            
            # Render children for this iteration
            result += self._render_children(element, loop_ctx)
        
        return result
    
    def _render_set(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Handle t-set directive"""
        var_name = directives['t-set']
        
        if 't-value' in directives:
            # Set variable to evaluated expression
            value = self.evaluator.evaluate(directives['t-value'], context)
            context[var_name] = value
        else:
            # Set variable to rendered content of element
            content = self._render_children(element, context)
            context[var_name] = content
        
        return ""  # t-set doesn't produce output
