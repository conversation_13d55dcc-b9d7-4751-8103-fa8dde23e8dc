"""
Application and web interface routes
"""
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
import re

from ..config import config
from ..database.registry import DatabaseRegistry
from ..utils.responses import APIResponse

router = APIRouter(prefix="/app", tags=["app"])


@router.get("/databases", response_class=HTMLResponse)
async def database_list_page(request: Request):
    """Database selection page"""
    if not config.list_db or not config.is_multi_db_mode:
        raise HTTPException(status_code=404, detail="Database listing is only available in multi-database mode")

    # This will need templates to be passed from the server instance
    # For now, return a simple HTML response
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Database Selection</title>
        <link rel="stylesheet" href="/static/css/database_list.css">
    </head>
    <body>
        <div class="container">
            <h1>Select Database</h1>
            <div id="database-list">
                <!-- Database list will be populated by JavaScript -->
            </div>
        </div>
        <script src="/static/js/database_list.js"></script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@router.get("")
async def app_home(request: Request, db: str = None):
    """Main application home page"""
    if db:
        # Database specified, validate and set context
        if config.is_multi_db_mode:
            # In multi-database mode, validate against filter
            if config.db_filter:
                if not re.match(config.db_filter, db):
                    raise HTTPException(status_code=400, detail="Database not accessible")

            # Set database context for this request
            DatabaseRegistry.set_current_database(db)
            return APIResponse.success({
                "message": f"Connected to database: {db}",
                "database": db,
                "mode": "multi-database"
            })
        else:
            # In single database mode, ignore the db parameter
            default_db = config.get_default_database()
            return APIResponse.success({
                "message": f"Using configured database: {default_db}",
                "database": default_db,
                "mode": "single-database"
            })
    else:
        # No database specified
        if config.is_multi_db_mode:
            # Redirect to database list
            return RedirectResponse(url="/app/database/list", status_code=302)
        else:
            # Use default database
            default_db = config.get_default_database()
            return APIResponse.success({
                "message": f"Using default database: {default_db}",
                "database": default_db,
                "mode": "single-database"
            })