# Cookie-Based Database Selection

This document explains the cookie-based database selection feature that allows users to persist their database choice across browser sessions.

## Overview

The ERP system supports multiple database selection methods with the following priority order:
1. **HTTP Header** (`X-Database`) - Highest priority
2. **Query Parameter** (`?db=database_name`) - Sets cookie and redirects
3. **Cookie** (`erp_database`) - Persists selection across sessions

## Implementation Details

### 1. Middleware (`erp/utils/middleware.py`)

The `database_middleware` function handles database selection logic:

```python
async def database_middleware(request: Request, call_next):
    """Database selection middleware with cookie support"""
    
    # Priority 1: Check X-Database header
    db_name = request.headers.get("X-Database")
    
    # Priority 2: Check query parameter (and set cookie)
    if not db_name:
        db_name = request.query_params.get("db")
        if db_name:
            # Set cookie when database is specified via query param
            response = await call_next(request)
            response.set_cookie(
                key="erp_database",
                value=db_name,
                max_age=30 * 24 * 60 * 60,  # 30 days
                httponly=True,
                secure=request.url.scheme == "https",
                samesite="lax"
            )
            return response
    
    # Priority 3: Check cookie
    if not db_name:
        db_name = request.cookies.get("erp_database")
    
    # Store in request state for use by routes
    request.state.db_name = db_name
    
    return await call_next(request)
```

### 2. Route Handling (`erp/routes/app.py`)

The app routes use the database name from `request.state.db_name`:

```python
@router.get("/app")
async def app_home(request: Request):
    """Main application home page"""
    
    # Get database from middleware (query param or cookie)
    db_name = getattr(request.state, 'db_name', None)
    
    if not db_name:
        # Redirect to database selection if no database specified
        return RedirectResponse(url="/app/database/list", status_code=302)
    
    # Validate database exists and is accessible
    if not await DatabaseRegistry.is_database_accessible(db_name):
        return RedirectResponse(url="/app/database/list", status_code=302)
    
    # Set current database and render app
    await DatabaseRegistry.set_current_database(db_name)
    return templates.TemplateResponse("app/home.html", {
        "request": request,
        "database": db_name
    })
```

### 3. Client-Side JavaScript (`static/js/database_list.js`)

The JavaScript handles cookie reading and UI updates:

```javascript
class DatabaseList {
    getCurrentDatabase() {
        // Read erp_database cookie
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'erp_database') {
                return decodeURIComponent(value);
            }
        }
        return null;
    }
    
    highlightCurrentDatabase() {
        const currentDb = this.getCurrentDatabase();
        if (!currentDb) return;
        
        // Find and highlight current database card
        const cards = document.querySelectorAll('.database-card');
        cards.forEach(card => {
            const dbName = card.dataset.database;
            if (dbName === currentDb) {
                card.classList.add('current-database');
                
                // Add current indicator
                const header = card.querySelector('.database-header');
                if (header && !header.querySelector('.current-indicator')) {
                    const indicator = document.createElement('div');
                    indicator.className = 'current-indicator';
                    indicator.innerHTML = '<i class="fas fa-check-circle"></i> Current';
                    header.appendChild(indicator);
                }
            }
        });
    }
}
```

### 4. CSS Styling (`static/css/database_list.css`)

Visual styling for the current database indicator:

```css
.database-card.current-database {
    border-color: #4caf50;
    background: #f1f8e9;
    position: relative;
}

.database-card.current-database::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #4caf50, #66bb6a);
    border-radius: 14px;
    z-index: -1;
}

.current-indicator {
    background: #4caf50;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}
```

## Usage Examples

### 1. Setting Database via Query Parameter

```bash
# This will set the cookie and redirect
curl -X GET "http://localhost:8000/app?db=my_database"
```

### 2. Using Header-Based Selection

```bash
# This overrides any existing cookie
curl -X GET "http://localhost:8000/app" -H "X-Database: my_database"
```

### 3. Relying on Cookie

```bash
# After setting via query param, subsequent requests use the cookie
curl -X GET "http://localhost:8000/app" -b "erp_database=my_database"
```

## Cookie Details

- **Name**: `erp_database`
- **Max Age**: 30 days (2,592,000 seconds)
- **HttpOnly**: `true` (prevents JavaScript access for security)
- **Secure**: `true` when using HTTPS
- **SameSite**: `lax` (allows cross-site navigation)

## Security Considerations

1. **Database Validation**: All database names are validated against the accessible database list
2. **HttpOnly Cookie**: Prevents XSS attacks from accessing the cookie
3. **Secure Flag**: Ensures cookie is only sent over HTTPS in production
4. **SameSite Protection**: Prevents CSRF attacks while allowing legitimate navigation

## Testing

Use the provided test script to verify functionality:

```bash
python test_cookie_database_selection.py
```

The test script verifies:
- Database listing
- Cookie setting via query parameters
- Cookie-based database selection
- Header-based override
- UI highlighting of current database

## Browser Developer Tools

To inspect the cookie in browser developer tools:
1. Open Developer Tools (F12)
2. Go to Application/Storage tab
3. Look under Cookies for your domain
4. Find the `erp_database` cookie

## Troubleshooting

### Cookie Not Being Set
- Check that the database name is valid
- Verify the query parameter format: `?db=database_name`
- Ensure the server is accessible

### Cookie Not Being Read
- Check cookie expiration
- Verify the domain matches
- Ensure HttpOnly flag allows server-side reading

### Database Selection Not Working
- Verify database exists in the system
- Check database accessibility permissions
- Review server logs for validation errors

## Future Enhancements

Potential improvements to consider:
1. **User Preferences**: Store additional user preferences in cookies
2. **Session Management**: Integrate with user authentication
3. **Database Groups**: Support for database categorization
4. **Recent Databases**: Track recently accessed databases
5. **Auto-Selection**: Remember last used database per user