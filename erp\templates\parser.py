"""
XML Template Parser - Core parsing functionality
"""
import xml.etree.ElementTree as ET
from typing import Dict, Any, List, Optional
import re
from .exceptions import TemplateSyntaxError


class TemplateParser:
    """Parses XML templates and extracts template definitions"""
    
    def __init__(self):
        self.templates: Dict[str, ET.Element] = {}
    
    def parse_template_file(self, content: str) -> Dict[str, ET.Element]:
        """Parse template file content and extract template definitions"""
        try:
            # Parse XML content
            root = ET.fromstring(content)
            
            # Find all template definitions
            templates = {}
            for template_elem in root.findall('.//t[@t-name]'):
                template_name = template_elem.get('t-name')
                if template_name:
                    templates[template_name] = template_elem
            
            return templates
            
        except ET.ParseError as e:
            raise TemplateSyntaxError(f"Invalid XML syntax: {e}")
    
    def extract_directives(self, element: ET.Element) -> Dict[str, str]:
        """Extract template directives from element attributes"""
        directives = {}
        
        for attr_name, attr_value in element.attrib.items():
            if attr_name.startswith('t-'):
                directives[attr_name] = attr_value
        
        return directives
    
    def is_template_element(self, element: ET.Element) -> bool:
        """Check if element is a template control element (t tag)"""
        return element.tag == 't'
    
    def has_directives(self, element: ET.Element) -> bool:
        """Check if element has any template directives"""
        return any(attr.startswith('t-') for attr in element.attrib.keys())


class ExpressionEvaluator:
    """Evaluates template expressions safely"""
    
    def __init__(self):
        # Safe built-in functions for template expressions
        self.safe_builtins = {
            'str': str,
            'int': int,
            'float': float,
            'bool': bool,
            'len': len,
            'range': range,
            'enumerate': enumerate,
            'zip': zip,
            'min': min,
            'max': max,
            'sum': sum,
            'abs': abs,
            'round': round,
        }
    
    def evaluate(self, expression: str, context: Dict[str, Any]) -> Any:
        """Safely evaluate a template expression"""
        try:
            # Create safe evaluation environment
            safe_dict = {
                '__builtins__': self.safe_builtins,
                **context
            }
            
            # Evaluate expression
            return eval(expression, safe_dict)
            
        except Exception as e:
            raise TemplateSyntaxError(f"Error evaluating expression '{expression}': {e}")
    
    def evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """Evaluate a boolean condition"""
        result = self.evaluate(condition, context)
        return bool(result)
    
    def parse_foreach_expression(self, expression: str) -> tuple[str, str]:
        """Parse t-foreach expression to extract iterable and variable name"""
        # Handle expressions like "doc.order_line" with t-as="line"
        # For now, just return the expression as iterable
        return expression.strip(), "item"


class LoopContext:
    """Context for template loops (t-foreach)"""
    
    def __init__(self, items: List[Any], variable_name: str):
        self.items = items
        self.variable_name = variable_name
        self.index = 0
        self.length = len(items)
    
    @property
    def current_item(self) -> Any:
        """Get current loop item"""
        if 0 <= self.index < self.length:
            return self.items[self.index]
        return None
    
    @property
    def loop_vars(self) -> Dict[str, Any]:
        """Get loop variables (index, first, last, etc.)"""
        loop_obj = type('LoopObject', (), {
            'index': self.index + 1,  # 1-based index
            'index0': self.index,     # 0-based index
            'first': self.index == 0,
            'last': self.index == self.length - 1,
            'length': self.length,
            'revindex': self.length - self.index,
            'revindex0': self.length - self.index - 1,
        })()

        return {
            'loop': loop_obj
        }
    
    def next(self) -> bool:
        """Move to next item, return True if successful"""
        self.index += 1
        return self.index < self.length
