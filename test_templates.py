#!/usr/bin/env python3
"""
Test script for the template engine
"""
import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from erp.templates.manager import AsyncTemplateManager


async def test_template_engine():
    """Test the template engine with sample templates"""
    print("Testing ERP Template Engine...")
    
    # Create template manager
    manager = AsyncTemplateManager()

    # Clear any existing cache
    manager.clear_cache()

    # Add templates directory
    manager.add_template_directory("templates")
    
    try:
        # Test 1: Load and render database list template
        print("\n1. Testing database list template...")
        await manager.load_template_file_async("database_list.xml")
        
        # Create a mock config object with attributes
        class MockConfig:
            def __init__(self):
                self.is_multi_db_mode = True
                self.list_db = True

        context = {
            'title': 'Database Selection',
            'config': MockConfig()
        }
        
        result = await manager.render_template_async('database_list.html', context)
        print(f"✓ Database list template rendered ({len(result)} characters)")
        
        # Save output for inspection
        with open("output_database_list.html", "w", encoding="utf-8") as f:
            f.write(result)
        print("  → Saved to output_database_list.html")
        
    except Exception as e:
        print(f"✗ Database list template failed: {e}")
    
    try:
        # Test 2: Load and render app home template
        print("\n2. Testing app home template...")
        await manager.load_template_file_async("app_home.xml")
        
        context = {
            'title': 'ERP System Dashboard',
            'subtitle': 'Welcome to your ERP system',
            'database_name': 'demo_db',
            'mode': 'multi-database',
            'database_connected': True,
            'addon_count': 5,
            'server_status': 'Running',
            'show_debug_info': False
        }
        
        result = await manager.render_template_async('app_home.html', context)
        print(f"✓ App home template rendered ({len(result)} characters)")
        
        # Save output for inspection
        with open("output_app_home.html", "w", encoding="utf-8") as f:
            f.write(result)
        print("  → Saved to output_app_home.html")
        
    except Exception as e:
        print(f"✗ App home template failed: {e}")
    
    try:
        # Test 3: Load and render demo template
        print("\n3. Testing demo template...")
        await manager.load_template_file_async("demo_template.xml")
        
        context = {}
        
        result = await manager.render_template_async('demo.template', context)
        print(f"✓ Demo template rendered ({len(result)} characters)")
        
        # Save output for inspection
        with open("output_demo.html", "w", encoding="utf-8") as f:
            f.write(result)
        print("  → Saved to output_demo.html")
        
    except Exception as e:
        print(f"✗ Demo template failed: {e}")
    
    # Test 4: List loaded templates
    print("\n4. Loaded templates:")
    templates = manager.list_templates()
    for template in templates:
        print(f"  - {template}")
    
    print(f"\nTotal templates loaded: {len(templates)}")
    print("\nTemplate engine test completed!")


if __name__ == "__main__":
    asyncio.run(test_template_engine())
