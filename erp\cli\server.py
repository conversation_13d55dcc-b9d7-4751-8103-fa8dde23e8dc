"""
Server management commands
"""
import os
import sys
import signal
import psutil
import argparse
from typing import Optional, List
import json
import time

from .base import BaseCommand, CommandGroup
from ..config import config
# Import create_app only when needed to avoid circular imports
def get_create_app():
    """Lazy import of create_app to avoid circular imports"""
    from ..server import create_app
    return create_app


class ServerPidManager:
    """Manages server process ID file"""
    
    def __init__(self, pid_file: str = "erp-server.pid"):
        self.pid_file = pid_file
    
    def write_pid(self, pid: int):
        """Write PID to file"""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(pid))
        except Exception as e:
            print(f"Warning: Could not write PID file: {e}")
    
    def read_pid(self) -> Optional[int]:
        """Read PID from file"""
        try:
            if os.path.exists(self.pid_file):
                with open(self.pid_file, 'r') as f:
                    return int(f.read().strip())
        except (ValueError, FileNotFoundError):
            pass
        return None
    
    def remove_pid_file(self):
        """Remove PID file"""
        try:
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
        except Exception as e:
            print(f"Warning: Could not remove PID file: {e}")
    
    def is_process_running(self, pid: int) -> bool:
        """Check if process is running"""
        try:
            return psutil.pid_exists(pid)
        except Exception:
            return False
    
    def get_process_info(self, pid: int) -> Optional[dict]:
        """Get process information"""
        try:
            if self.is_process_running(pid):
                proc = psutil.Process(pid)
                return {
                    'pid': pid,
                    'name': proc.name(),
                    'status': proc.status(),
                    'create_time': proc.create_time(),
                    'cpu_percent': proc.cpu_percent(),
                    'memory_info': proc.memory_info()._asdict(),
                    'cmdline': proc.cmdline()
                }
        except Exception:
            pass
        return None


class StartCommand(BaseCommand):
    """Start ERP server command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('--host', default='127.0.0.1', help='Server host')
        parser.add_argument('--port', type=int, default=8069, help='Server port')
        parser.add_argument('--debug', action='store_true', help='Debug mode')
        parser.add_argument('--reload', action='store_true', help='Auto-reload')
        parser.add_argument('--workers', type=int, default=1, help='Worker processes')
        parser.add_argument('--daemon', action='store_true', help='Run as daemon')
        parser.add_argument('--pid-file', default='erp-server.pid', help='PID file path')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle start command"""
        pid_manager = ServerPidManager(args.pid_file)
        
        # Check if server is already running
        existing_pid = pid_manager.read_pid()
        if existing_pid and pid_manager.is_process_running(existing_pid):
            self.print_error(f"Server is already running (PID: {existing_pid})")
            return 1
        
        self.print_info("Starting ERP ASGI server...")
        self.print_info(f"Configuration: {config.config_file}")
        self.print_info(f"Database: {config.get('options', 'db_name')}")
        self.print_info(f"Addons path: {config.addons_path}")
        self.print_info(f"Server will listen on {args.host}:{args.port}")
        
        if args.daemon:
            return self._start_daemon(args, pid_manager)
        else:
            return self._start_server(args, pid_manager)
    
    def _start_server(self, args: argparse.Namespace, pid_manager: ServerPidManager) -> int:
        """Start server in foreground"""
        try:
            import uvicorn
            
            # Write PID file
            pid_manager.write_pid(os.getpid())
            
            # Setup signal handlers for graceful shutdown
            def signal_handler(signum, frame):
                self.print_info("Received shutdown signal, stopping server...")
                pid_manager.remove_pid_file()
                sys.exit(0)
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            create_app = get_create_app()
            app = create_app()
            
            uvicorn.run(
                app,
                host=args.host,
                port=args.port,
                reload=args.reload,
                workers=args.workers if not args.reload else 1,
                log_level="debug" if args.debug else "info",
                access_log=True
            )
            return 0
        except Exception as e:
            self.print_error(f"Failed to start server: {e}")
            pid_manager.remove_pid_file()
            return 1
    
    def _start_daemon(self, args: argparse.Namespace, pid_manager: ServerPidManager) -> int:
        """Start server as daemon"""
        try:
            # Fork process
            pid = os.fork()
            if pid > 0:
                # Parent process
                pid_manager.write_pid(pid)
                self.print_success(f"Server started as daemon (PID: {pid})")
                return 0
            
            # Child process - become daemon
            os.setsid()
            os.chdir('/')
            os.umask(0)
            
            # Redirect standard file descriptors
            with open('/dev/null', 'r') as dev_null:
                os.dup2(dev_null.fileno(), sys.stdin.fileno())
            with open('/dev/null', 'w') as dev_null:
                os.dup2(dev_null.fileno(), sys.stdout.fileno())
                os.dup2(dev_null.fileno(), sys.stderr.fileno())
            
            # Start server
            import uvicorn
            create_app = get_create_app()
            app = create_app()
            
            uvicorn.run(
                app,
                host=args.host,
                port=args.port,
                workers=args.workers,
                log_level="debug" if args.debug else "info",
                access_log=True
            )
            
        except Exception as e:
            self.print_error(f"Failed to start daemon: {e}")
            return 1


class StopCommand(BaseCommand):
    """Stop ERP server command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('--pid-file', default='erp-server.pid', help='PID file path')
        parser.add_argument('--force', action='store_true', help='Force kill server')
        parser.add_argument('--timeout', type=int, default=30, help='Timeout for graceful shutdown')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle stop command"""
        pid_manager = ServerPidManager(args.pid_file)
        
        pid = pid_manager.read_pid()
        if not pid:
            self.print_error("No PID file found. Server may not be running.")
            return 1
        
        if not pid_manager.is_process_running(pid):
            self.print_warning(f"Process {pid} is not running. Cleaning up PID file.")
            pid_manager.remove_pid_file()
            return 0
        
        self.print_info(f"Stopping server (PID: {pid})...")
        
        try:
            if args.force:
                # Force kill
                os.kill(pid, signal.SIGKILL)
                self.print_success("Server force killed")
            else:
                # Graceful shutdown
                os.kill(pid, signal.SIGTERM)
                
                # Wait for process to terminate
                timeout = args.timeout
                while timeout > 0 and pid_manager.is_process_running(pid):
                    time.sleep(1)
                    timeout -= 1
                
                if pid_manager.is_process_running(pid):
                    self.print_warning("Graceful shutdown timed out, force killing...")
                    os.kill(pid, signal.SIGKILL)
                    self.print_success("Server force killed")
                else:
                    self.print_success("Server stopped gracefully")
            
            pid_manager.remove_pid_file()
            return 0
            
        except ProcessLookupError:
            self.print_warning("Process not found. Cleaning up PID file.")
            pid_manager.remove_pid_file()
            return 0
        except PermissionError:
            self.print_error("Permission denied. Cannot stop server.")
            return 1
        except Exception as e:
            self.print_error(f"Failed to stop server: {e}")
            return 1


class StatusCommand(BaseCommand):
    """Show ERP server status command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('--pid-file', default='erp-server.pid', help='PID file path')
        parser.add_argument('--detailed', action='store_true', help='Show detailed status')
        parser.add_argument('--json', action='store_true', help='Output in JSON format')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle status command"""
        pid_manager = ServerPidManager(args.pid_file)
        
        pid = pid_manager.read_pid()
        status_data = {
            'running': False,
            'pid': None,
            'pid_file': args.pid_file,
            'config_file': config.config_file,
            'server_config': config.server_config,
            'database_config': {
                'host': config.db_config['host'],
                'port': config.db_config['port'],
                'database': config.db_config['database']
            }
        }
        
        if pid:
            if pid_manager.is_process_running(pid):
                status_data['running'] = True
                status_data['pid'] = pid
                
                if args.detailed:
                    proc_info = pid_manager.get_process_info(pid)
                    if proc_info:
                        status_data['process_info'] = proc_info
            else:
                # PID file exists but process is not running
                self.print_warning("PID file exists but process is not running. Cleaning up...")
                pid_manager.remove_pid_file()
        
        if args.json:
            print(json.dumps(status_data, indent=2, default=str))
        else:
            self._print_status(status_data, args.detailed)
        
        return 0 if status_data['running'] else 1
    
    def _print_status(self, status_data: dict, detailed: bool):
        """Print status in human-readable format"""
        print("ERP Server Status")
        print("=" * 50)
        
        if status_data['running']:
            self.print_success(f"Server is running (PID: {status_data['pid']})")
        else:
            self.print_error("Server is not running")
        
        print(f"PID file: {status_data['pid_file']}")
        print(f"Config file: {status_data['config_file']}")
        print(f"Server: {status_data['server_config']['host']}:{status_data['server_config']['port']}")
        print(f"Database: {status_data['database_config']['host']}:{status_data['database_config']['port']}/{status_data['database_config']['database']}")
        
        if detailed and status_data.get('process_info'):
            proc_info = status_data['process_info']
            print("\nProcess Details:")
            print(f"  Name: {proc_info['name']}")
            print(f"  Status: {proc_info['status']}")
            print(f"  CPU: {proc_info['cpu_percent']:.1f}%")
            print(f"  Memory: {proc_info['memory_info']['rss'] / 1024 / 1024:.1f} MB")
            print(f"  Started: {time.ctime(proc_info['create_time'])}")


class RestartCommand(BaseCommand):
    """Restart ERP server command"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('--pid-file', default='erp-server.pid', help='PID file path')
        parser.add_argument('--host', default='127.0.0.1', help='Server host')
        parser.add_argument('--port', type=int, default=8069, help='Server port')
        parser.add_argument('--debug', action='store_true', help='Debug mode')
        parser.add_argument('--reload', action='store_true', help='Auto-reload')
        parser.add_argument('--workers', type=int, default=1, help='Worker processes')
        parser.add_argument('--daemon', action='store_true', help='Run as daemon')
        parser.add_argument('--timeout', type=int, default=30, help='Timeout for graceful shutdown')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle restart command"""
        # Stop server first
        stop_cmd = StopCommand()
        stop_result = stop_cmd.handle(args)
        
        if stop_result != 0:
            self.print_warning("Stop command failed, but continuing with start...")
        
        # Wait a moment
        time.sleep(2)
        
        # Start server
        start_cmd = StartCommand()
        return start_cmd.handle(args)


class ServerCommandGroup(CommandGroup):
    """Server command group"""
    
    def __init__(self):
        super().__init__()
        self.register_command(StartCommand())
        self.register_command(StopCommand())
        self.register_command(StatusCommand())
        self.register_command(RestartCommand())
    
    def add_commands(self, subparsers):
        """Add server commands to subparsers"""
        # Start server
        start_parser = subparsers.add_parser('start', help='Start ERP ASGI server')
        self.commands['start'].add_arguments(start_parser)
        
        # Stop server
        stop_parser = subparsers.add_parser('stop', help='Stop ERP server')
        self.commands['stop'].add_arguments(stop_parser)
        
        # Server status
        status_parser = subparsers.add_parser('status', help='Show server status')
        self.commands['status'].add_arguments(status_parser)
        
        # Restart server
        restart_parser = subparsers.add_parser('restart', help='Restart ERP server')
        self.commands['restart'].add_arguments(restart_parser)