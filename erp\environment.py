"""
Environment system for ERP
Provides Odoo-like Environment with cr, uid, context properties
"""
from typing import Dict, Any, Optional, Union, TYPE_CHECKING
from contextlib import asynccontextmanager
import asyncio
from .context import ContextManager

if TYPE_CHECKING:
    from .database.connection import DatabaseManager


class DatabaseCursor:
    """
    Database cursor wrapper that provides Odoo-like interface
    """
    
    def __init__(self, db_manager: 'DatabaseManager', db_name: str):
        self._db_manager = db_manager
        self.db_name = db_name
        self._connection = None
        self._in_transaction = False
    
    @asynccontextmanager
    async def _get_connection(self):
        """Get database connection from pool"""
        async with self._db_manager.acquire_connection() as conn:
            self._connection = conn
            try:
                yield conn
            finally:
                self._connection = None
    
    async def execute(self, query: str, *args) -> str:
        """Execute a query"""
        if self._connection:
            return await self._connection.execute(query, *args)
        else:
            return await self._db_manager.execute(query, *args)
    
    async def fetch(self, query: str, *args) -> list:
        """Fetch multiple rows"""
        if self._connection:
            return await self._connection.fetch(query, *args)
        else:
            return await self._db_manager.fetch(query, *args)
    
    async def fetchrow(self, query: str, *args) -> Optional[dict]:
        """Fetch single row"""
        if self._connection:
            result = await self._connection.fetchrow(query, *args)
        else:
            result = await self._db_manager.fetchrow(query, *args)
        
        return dict(result) if result else None
    
    async def fetchval(self, query: str, *args) -> Any:
        """Fetch single value"""
        if self._connection:
            return await self._connection.fetchval(query, *args)
        else:
            return await self._db_manager.fetchval(query, *args)
    
    async def commit(self):
        """Commit transaction"""
        if self._connection and self._in_transaction:
            await self._connection.execute("COMMIT")
            self._in_transaction = False
    
    async def rollback(self):
        """Rollback transaction"""
        if self._connection and self._in_transaction:
            await self._connection.execute("ROLLBACK")
            self._in_transaction = False
    
    async def begin(self):
        """Begin transaction"""
        if self._connection and not self._in_transaction:
            await self._connection.execute("BEGIN")
            self._in_transaction = True
    
    @property
    def in_transaction(self) -> bool:
        """Check if cursor is in transaction"""
        return self._in_transaction


class Environment:
    """
    Odoo-like Environment providing access to cr, uid, context
    """
    
    def __init__(self, cr: DatabaseCursor, uid: int, context: Dict[str, Any] = None):
        if cr is None:
            raise ValueError("Database cursor (cr) is mandatory")
        if uid is None:
            raise ValueError("User ID (uid) is mandatory")
        
        self._cr = cr
        self._uid = uid
        self._context = context or {}
        self._models = {}
    
    @property
    def cr(self) -> DatabaseCursor:
        """Database cursor"""
        return self._cr
    
    @property
    def uid(self) -> int:
        """User ID"""
        return self._uid
    
    @property
    def context(self) -> Dict[str, Any]:
        """Context dictionary"""
        return self._context.copy()
    
    def with_context(self, **context) -> 'Environment':
        """Create new environment with updated context"""
        new_context = {**self._context, **context}
        return Environment(self._cr, self._uid, new_context)
    
    def with_user(self, uid: int) -> 'Environment':
        """Create new environment with different user"""
        return Environment(self._cr, uid, self._context)
    
    def __getitem__(self, model_name: str):
        """Get model by name (similar to Odoo's env['model.name'])"""
        if model_name not in self._models:
            from .models.base import ModelRegistry
            model_class = ModelRegistry.get(model_name)
            if model_class:
                # Create model instance with this environment
                self._models[model_name] = model_class(env=self)
            else:
                raise KeyError(f"Model {model_name} not found in registry")
        return self._models[model_name]
    
    def ref(self, xml_id: str):
        """Get record by XML ID"""
        # TODO: Implement XML ID resolution
        # For now, return a placeholder
        parts = xml_id.split('.')
        if len(parts) == 2:
            module, xml_id = parts
            # This would normally query ir.model.data
            return None
        raise ValueError(f"Invalid XML ID format: {xml_id}")
    
    async def __aenter__(self):
        """Async context manager entry"""
        # Set this environment in the context
        ContextManager.set_environment(self)
        ContextManager.set_database(self._cr.db_name)
        ContextManager.set_user(self._uid)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        # Context will be automatically reset when the context var goes out of scope
        pass
    
    def __repr__(self):
        return f"Environment(cr={self._cr.db_name}, uid={self._uid}, context={self._context})"


class EnvironmentManager:
    """
    Manager for creating and managing environments
    """
    
    @classmethod
    async def create_environment(
        cls, 
        db_name: str, 
        uid: int, 
        context: Dict[str, Any] = None
    ) -> Environment:
        """Create a new environment"""
        from .database.registry import DatabaseRegistry
        
        # Get database manager
        db_manager = await DatabaseRegistry.get_database(db_name)
        
        # Create cursor
        cr = DatabaseCursor(db_manager, db_name)
        
        # Create environment
        env = Environment(cr, uid, context)
        
        return env
    
    @classmethod
    def get_current_environment(cls) -> Optional[Environment]:
        """Get current environment from context"""
        return ContextManager.get_environment()
    
    @classmethod
    async def with_environment(
        cls, 
        db_name: str, 
        uid: int, 
        context: Dict[str, Any] = None
    ):
        """Context manager for running code with specific environment"""
        env = await cls.create_environment(db_name, uid, context)
        return ContextManager.environment.run(env)
    
    @classmethod
    @asynccontextmanager
    async def transaction(cls, env: Environment = None):
        """Context manager for database transactions"""
        if env is None:
            env = cls.get_current_environment()
            if env is None:
                raise RuntimeError("No environment found in context")
        
        async with env.cr._get_connection():
            await env.cr.begin()
            try:
                yield env
                await env.cr.commit()
            except Exception:
                await env.cr.rollback()
                raise


# Convenience function to get current environment
def env() -> Optional[Environment]:
    """Get current environment from context"""
    return EnvironmentManager.get_current_environment()


# Convenience function to create environment
async def create_env(db_name: str, uid: int, context: Dict[str, Any] = None) -> Environment:
    """Create a new environment"""
    return await EnvironmentManager.create_environment(db_name, uid, context)
