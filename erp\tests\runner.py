"""
Test runner for ERP testing framework
Provides test discovery, execution, and reporting
"""
import unittest
import sys
import os
import importlib
import inspect
from typing import List, Dict, Set, Optional, Union, Type
from pathlib import Path

from .tags import get_test_tags, should_run_test, filter_tests_by_tags, Tags
from .common import TestCase, TransactionCase, SingleTransactionCase
from .discovery import TestDiscovery
from .isolation import test_isolation, addon_test_isolation


class TestSuite:
    """Test suite for organizing and running tests"""
    
    def __init__(self, name: str = "ERP Test Suite"):
        self.name = name
        self.tests: List[unittest.TestCase] = []
        self.test_classes: List[Type[unittest.TestCase]] = []
        self.include_tags: Set[str] = set()
        self.exclude_tags: Set[str] = set()
    
    def add_test(self, test: unittest.TestCase):
        """Add a test to the suite"""
        self.tests.append(test)
    
    def add_test_class(self, test_class: Type[unittest.TestCase]):
        """Add a test class to the suite"""
        self.test_classes.append(test_class)
    
    def set_tag_filter(self, include_tags: Union[str, List[str]] = None, 
                      exclude_tags: Union[str, List[str]] = None):
        """Set tag filters for the suite"""
        if isinstance(include_tags, str):
            self.include_tags = {include_tags}
        elif isinstance(include_tags, list):
            self.include_tags = set(include_tags)
        elif include_tags:
            self.include_tags = set(include_tags)
        
        if isinstance(exclude_tags, str):
            self.exclude_tags = {exclude_tags}
        elif isinstance(exclude_tags, list):
            self.exclude_tags = set(exclude_tags)
        elif exclude_tags:
            self.exclude_tags = set(exclude_tags)
    
    def build_unittest_suite(self) -> unittest.TestSuite:
        """Build a unittest.TestSuite from this test suite"""
        suite = unittest.TestSuite()
        
        # Add individual tests
        filtered_tests = filter_tests_by_tags(
            self.tests, self.include_tags, self.exclude_tags
        )
        for test in filtered_tests:
            suite.addTest(test)
        
        # Add test classes
        for test_class in self.test_classes:
            if should_run_test(test_class, self.include_tags, self.exclude_tags):
                # Load all test methods from the class
                loader = unittest.TestLoader()
                class_suite = loader.loadTestsFromTestCase(test_class)
                
                # Filter individual test methods by tags
                filtered_class_tests = []
                for test in class_suite:
                    if should_run_test(test, self.include_tags, self.exclude_tags):
                        filtered_class_tests.append(test)
                
                # Add filtered tests to suite
                for test in filtered_class_tests:
                    suite.addTest(test)
        
        return suite


class TestRunner:
    """Test runner for ERP tests with addon support"""

    def __init__(self, verbosity: int = 2, use_isolation: bool = True):
        self.verbosity = verbosity
        self.use_isolation = use_isolation
        self.test_suite = TestSuite()
        self.discovery = TestDiscovery()
        self.discovered_tests: Dict[str, List[Type[unittest.TestCase]]] = {}
    
    def discover_tests(self, test_paths: List[str] = None,
                      addon_names: List[str] = None) -> TestSuite:
        """
        Discover tests from specified paths or addons

        Args:
            test_paths: List of paths to search for tests
            addon_names: List of addon names to search for tests

        Returns:
            TestSuite with discovered tests
        """
        if test_paths is None:
            test_paths = ['tests']

        # Use the new discovery system
        if test_paths and 'tests' in test_paths:
            self.discovery.discover_core_tests()

        if addon_names:
            self.discovery.discover_addon_tests(addon_names)

        # Add discovered tests to our suite
        for addon_name, test_classes in self.discovery.discovered_tests.items():
            for test_class in test_classes:
                self.test_suite.add_test_class(test_class)

        return self.test_suite
    
    def _discover_tests_in_path(self, test_path: str):
        """Discover tests in a specific path"""
        if not os.path.exists(test_path):
            print(f"Warning: Test path {test_path} does not exist")
            return
        
        # Use unittest's test discovery
        loader = unittest.TestLoader()
        suite = loader.discover(test_path, pattern='test*.py')
        
        # Extract test classes from the suite
        for test_group in suite:
            if hasattr(test_group, '_tests'):
                for test_case in test_group._tests:
                    if hasattr(test_case, '_tests'):
                        for test in test_case._tests:
                            test_class = test.__class__
                            if test_class not in self.test_suite.test_classes:
                                self.test_suite.add_test_class(test_class)
    
    def _discover_addon_tests(self, addon_names: List[str]):
        """Discover tests in specified addons"""
        from erp.addons.loader import AddonLoader
        
        loader = AddonLoader()
        
        for addon_name in addon_names:
            addon_path = os.path.join(loader.addons_path, addon_name)
            tests_path = os.path.join(addon_path, 'tests')
            
            if os.path.exists(tests_path):
                self._discover_tests_in_path(tests_path)
                self.discovered_tests[addon_name] = self.test_suite.test_classes.copy()
    
    def run_tests(self, include_tags: Union[str, List[str]] = None,
                 exclude_tags: Union[str, List[str]] = None,
                 test_pattern: str = None) -> unittest.TestResult:
        """
        Run tests with optional filtering and isolation

        Args:
            include_tags: Tags to include
            exclude_tags: Tags to exclude
            test_pattern: Pattern to match test names

        Returns:
            Test result
        """
        if self.use_isolation:
            with test_isolation():
                return self._run_tests_internal(include_tags, exclude_tags, test_pattern)
        else:
            return self._run_tests_internal(include_tags, exclude_tags, test_pattern)

    def _run_tests_internal(self, include_tags, exclude_tags, test_pattern):
        """Internal test running method"""
        # Set tag filters
        self.test_suite.set_tag_filter(include_tags, exclude_tags)

        # Build unittest suite
        unittest_suite = self.test_suite.build_unittest_suite()

        # Apply pattern filter if specified
        if test_pattern:
            unittest_suite = self._filter_by_pattern(unittest_suite, test_pattern)

        # Run tests
        runner = unittest.TextTestRunner(verbosity=self.verbosity)
        result = runner.run(unittest_suite)

        return result
    
    def _filter_by_pattern(self, suite: unittest.TestSuite, pattern: str) -> unittest.TestSuite:
        """Filter tests by name pattern"""
        filtered_suite = unittest.TestSuite()
        
        for test in suite:
            if hasattr(test, '_testMethodName'):
                test_name = f"{test.__class__.__name__}.{test._testMethodName}"
                if pattern.lower() in test_name.lower():
                    filtered_suite.addTest(test)
            elif hasattr(test, '_tests'):
                # Handle nested suites
                nested_filtered = self._filter_by_pattern(test, pattern)
                if nested_filtered.countTestCases() > 0:
                    filtered_suite.addTest(nested_filtered)
        
        return filtered_suite
    
    def list_tests(self, include_tags: Union[str, List[str]] = None,
                  exclude_tags: Union[str, List[str]] = None) -> List[str]:
        """List available tests with optional filtering"""
        self.test_suite.set_tag_filter(include_tags, exclude_tags)
        unittest_suite = self.test_suite.build_unittest_suite()
        
        test_names = []
        for test in unittest_suite:
            if hasattr(test, '_testMethodName'):
                test_name = f"{test.__class__.__name__}.{test._testMethodName}"
                test_tags = get_test_tags(test)
                tag_str = f" [{', '.join(sorted(test_tags))}]" if test_tags else ""
                test_names.append(f"{test_name}{tag_str}")
        
        return test_names
    
    def run_addon_tests(self, addon_name: str, **kwargs) -> unittest.TestResult:
        """Run tests for a specific addon with isolation"""
        if self.use_isolation:
            with addon_test_isolation(addon_name):
                return self._run_addon_tests_internal(addon_name, **kwargs)
        else:
            return self._run_addon_tests_internal(addon_name, **kwargs)

    def _run_addon_tests_internal(self, addon_name: str, **kwargs) -> unittest.TestResult:
        """Internal addon test running method"""
        # Discover tests for the addon
        self.discovery.discover_addon_tests([addon_name])

        # Filter to only tests from this addon
        addon_tests = self.discovery.get_tests_by_addon(addon_name)
        if not addon_tests:
            print(f"No tests found for addon: {addon_name}")
            return unittest.TestResult()

        # Create a new test suite with only addon tests
        addon_suite = TestSuite(f"{addon_name} Tests")
        for test_class in addon_tests:
            addon_suite.add_test_class(test_class)

        # Temporarily replace the main suite
        original_suite = self.test_suite
        self.test_suite = addon_suite

        try:
            return self._run_tests_internal(
                kwargs.get('include_tags'),
                kwargs.get('exclude_tags'),
                kwargs.get('test_pattern')
            )
        finally:
            self.test_suite = original_suite
