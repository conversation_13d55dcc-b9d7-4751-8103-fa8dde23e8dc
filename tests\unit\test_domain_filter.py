"""
Test cases for DomainFilter utility
"""
import pytest
from erp.utils.domain import DomainFilter


class Mock<PERSON><PERSON>ord:
    """Mock record for testing domain filtering"""
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)


class TestDomainFilter:
    """Test cases for DomainFilter class"""
    
    def setup_method(self):
        """Set up test data"""
        self.records = [
            MockRecord(id='1', name='<PERSON>', age=25, active=True, department='Sales'),
            <PERSON>ckRecord(id='2', name='<PERSON>', age=30, active=False, department='IT'),
            MockRecord(id='3', name='<PERSON>', age=35, active=True, department='Sales'),
            <PERSON><PERSON>R<PERSON>ord(id='4', name='<PERSON>', age=28, active=True, department='HR'),
            MockRecord(id='5', name='Eve', age=32, active=False, department='IT'),
        ]
    
    def test_validate_domain_valid(self):
        """Test domain validation with valid domains"""
        valid_domains = [
            [('name', '=', 'Alice')],
            [('age', '>', 25), ('active', '=', True)],
            [('department', 'in', ['Sales', 'IT'])],
            []  # Empty domain should be valid
        ]
        
        for domain in valid_domains:
            assert DomainFilter.validate_domain(domain) is True
    
    def test_validate_domain_invalid(self):
        """Test domain validation with invalid domains"""
        invalid_domains = [
            "not a list",
            [('name',)],  # Missing operator and value
            [('name', '=', 'Alice', 'extra')],  # Too many elements
            [(123, '=', 'Alice')],  # Non-string field name
            [('name', 'invalid_op', 'Alice')],  # Invalid operator
        ]
        
        for domain in invalid_domains:
            with pytest.raises(ValueError):
                DomainFilter.validate_domain(domain)
    
    def test_build_sql_where_clause_simple(self):
        """Test SQL WHERE clause building with simple conditions"""
        domain = [('name', '=', 'Alice')]
        where_clause, params = DomainFilter.build_sql_where_clause(domain)
        
        assert where_clause == "name = $1"
        assert params == ['Alice']
    
    def test_build_sql_where_clause_multiple(self):
        """Test SQL WHERE clause building with multiple conditions"""
        domain = [
            ('age', '>', 25),
            ('active', '=', True),
            ('department', 'in', ['Sales', 'IT'])
        ]
        where_clause, params = DomainFilter.build_sql_where_clause(domain)
        
        expected_clause = "age > $1 AND active = $2 AND department IN ($3,$4)"
        assert where_clause == expected_clause
        assert params == [25, True, 'Sales', 'IT']
    
    def test_build_sql_where_clause_with_offset(self):
        """Test SQL WHERE clause building with parameter offset"""
        domain = [('name', '=', 'Alice')]
        where_clause, params = DomainFilter.build_sql_where_clause(domain, param_offset=5)
        
        assert where_clause == "name = $6"
        assert params == ['Alice']
    
    def test_build_sql_where_clause_empty_domain(self):
        """Test SQL WHERE clause building with empty domain"""
        where_clause, params = DomainFilter.build_sql_where_clause([])
        
        assert where_clause == ""
        assert params == []
    
    def test_filter_records_equals(self):
        """Test record filtering with equals operator"""
        domain = [('name', '=', 'Alice')]
        filtered = DomainFilter.filter_records(self.records, domain)
        
        assert len(filtered) == 1
        assert filtered[0].name == 'Alice'
    
    def test_filter_records_not_equals(self):
        """Test record filtering with not equals operator"""
        domain = [('department', '!=', 'Sales')]
        filtered = DomainFilter.filter_records(self.records, domain)
        
        assert len(filtered) == 3
        departments = [r.department for r in filtered]
        assert 'Sales' not in departments
    
    def test_filter_records_greater_than(self):
        """Test record filtering with greater than operator"""
        domain = [('age', '>', 30)]
        filtered = DomainFilter.filter_records(self.records, domain)
        
        assert len(filtered) == 2
        ages = [r.age for r in filtered]
        assert all(age > 30 for age in ages)
    
    def test_filter_records_in_operator(self):
        """Test record filtering with in operator"""
        domain = [('department', 'in', ['Sales', 'HR'])]
        filtered = DomainFilter.filter_records(self.records, domain)
        
        assert len(filtered) == 3
        departments = [r.department for r in filtered]
        assert all(dept in ['Sales', 'HR'] for dept in departments)
    
    def test_filter_records_multiple_conditions(self):
        """Test record filtering with multiple conditions (AND logic)"""
        domain = [
            ('active', '=', True),
            ('age', '>=', 30)
        ]
        filtered = DomainFilter.filter_records(self.records, domain)
        
        assert len(filtered) == 1
        assert filtered[0].name == 'Charlie'
        assert filtered[0].active is True
        assert filtered[0].age >= 30
    
    def test_filter_records_like_operator(self):
        """Test record filtering with like operator"""
        # Add records with names that can be tested with like
        test_records = [
            MockRecord(name='Alice Smith'),
            MockRecord(name='Bob Johnson'),
            MockRecord(name='Alice Brown'),
        ]
        
        domain = [('name', 'like', '%Alice%')]
        filtered = DomainFilter.filter_records(test_records, domain)
        
        assert len(filtered) == 2
        names = [r.name for r in filtered]
        assert all('Alice' in name for name in names)
    
    def test_filter_records_empty_domain(self):
        """Test record filtering with empty domain (should return all records)"""
        filtered = DomainFilter.filter_records(self.records, [])
        
        assert len(filtered) == len(self.records)
        assert filtered == self.records
    
    def test_filter_records_nonexistent_field(self):
        """Test record filtering with nonexistent field"""
        domain = [('nonexistent_field', '=', 'value')]
        filtered = DomainFilter.filter_records(self.records, domain)
        
        # Should return empty list since no records have the field
        assert len(filtered) == 0
    
    def test_combine_domains(self):
        """Test combining multiple domains"""
        domain1 = [('active', '=', True)]
        domain2 = [('age', '>', 25)]
        domain3 = [('department', '=', 'Sales')]
        
        combined = DomainFilter.combine_domains(domain1, domain2, domain3)
        
        expected = [
            ('active', '=', True),
            ('age', '>', 25),
            ('department', '=', 'Sales')
        ]
        assert combined == expected
    
    def test_combine_domains_with_empty(self):
        """Test combining domains with empty domains"""
        domain1 = [('active', '=', True)]
        domain2 = []
        domain3 = [('age', '>', 25)]
        
        combined = DomainFilter.combine_domains(domain1, domain2, domain3)
        
        expected = [
            ('active', '=', True),
            ('age', '>', 25)
        ]
        assert combined == expected
    
    def test_domain_to_string(self):
        """Test domain to string conversion"""
        domain = [
            ('name', '=', 'Alice'),
            ('age', '>', 25),
            ('active', '=', True)
        ]
        
        result = DomainFilter.domain_to_string(domain)
        expected = "name = 'Alice' AND age > 25 AND active = True"
        assert result == expected
    
    def test_domain_to_string_empty(self):
        """Test domain to string conversion with empty domain"""
        result = DomainFilter.domain_to_string([])
        assert result == "No filters"
    
    def test_sql_in_operator_empty_list(self):
        """Test SQL building with empty list for 'in' operator"""
        domain = [('department', 'in', [])]
        where_clause, params = DomainFilter.build_sql_where_clause(domain)
        
        assert where_clause == "FALSE"
        assert params == []
    
    def test_sql_not_in_operator_empty_list(self):
        """Test SQL building with empty list for 'not in' operator"""
        domain = [('department', 'not in', [])]
        where_clause, params = DomainFilter.build_sql_where_clause(domain)
        
        assert where_clause == "TRUE"
        assert params == []
    
    def test_special_operators(self):
        """Test special operators like =?, =like, =ilike"""
        # Test =? operator (equals or null)
        domain = [('field', '=?', 'value')]
        where_clause, params = DomainFilter.build_sql_where_clause(domain)
        assert where_clause == "(field = $1 OR field IS NULL)"
        assert params == ['value']
        
        # Test =like operator
        domain = [('field', '=like', 'value')]
        where_clause, params = DomainFilter.build_sql_where_clause(domain)
        assert where_clause == "(field = $1 OR field LIKE $2)"
        assert params == ['value', 'value']
        
        # Test =ilike operator
        domain = [('field', '=ilike', 'value')]
        where_clause, params = DomainFilter.build_sql_where_clause(domain)
        assert where_clause == "(field = $1 OR field ILIKE $2)"
        assert params == ['value', 'value']


if __name__ == '__main__':
    pytest.main([__file__])