"""
Server configuration and setup utilities
"""
from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from contextlib import asynccontextmanager
import os

from .config import config
from .database.registry import DatabaseRegistry
from .utils.middleware import (
    timing_middleware, error_handling_middleware, logging_middleware,
    database_middleware, environment_middleware
)


class ServerConfig:
    """Server configuration and setup"""
    
    def __init__(self, addon_loader=None):
        self.addon_loader = addon_loader
        self.templates = None
    
    def create_app(self) -> FastAPI:
        """Create FastAPI application with lifespan management"""
        
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # Startup
            print("Starting ERP server...")
            if self.addon_loader:
                await self._load_addons()
            yield
            # Shutdown
            print("Shutting down ERP server...")
            await DatabaseRegistry.close_all()
        
        return FastAPI(
            title="ERP System",
            description="Odoo-like ERP system with async support",
            version="1.0.0",
            lifespan=lifespan
        )
    
    def setup_static_files(self, app: FastAPI):
        """Setup static files and templates"""
        # Mount static files
        if os.path.exists("static"):
            app.mount("/static", StaticFiles(directory="static"), name="static")

        # Setup templates
        if os.path.exists("templates"):
            self.templates = Jinja2Templates(directory="templates")
        else:
            self.templates = None
    
    def setup_middleware(self, app: FastAPI):
        """Setup FastAPI middleware"""
        # CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=config.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Custom middleware (order matters - database_middleware must come before environment_middleware)
        app.middleware("http")(timing_middleware)
        app.middleware("http")(error_handling_middleware)
        app.middleware("http")(logging_middleware)
        app.middleware("http")(environment_middleware)
        app.middleware("http")(database_middleware)
    
    async def _load_addons(self):
        """Load all addons"""
        if not self.addon_loader:
            return
            
        try:
            await self.addon_loader.discover_addons()
            await self.addon_loader.load_addons()
            print(f"Loaded {len(self.addon_loader.loaded_addons)} addons")
        except Exception as e:
            print(f"Error loading addons: {e}")
            raise
    
    def get_addon_info(self):
        """Get information about loaded addons"""
        if not self.addon_loader:
            return {}
            
        addons_info = {}
        for name, manifest in self.addon_loader.loaded_addons.items():
            addons_info[name] = {
                "name": manifest.name,
                "version": manifest.version,
                "description": manifest.description,
                "depends": manifest.depends,
                "installable": manifest.installable
            }
        return addons_info