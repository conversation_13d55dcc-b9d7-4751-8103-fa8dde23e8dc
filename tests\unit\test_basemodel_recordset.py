"""
Tests for BaseModel integration with RecordSet
"""
import sys
import os

# Add ERP core to path for testing
erp_path = os.path.join(os.path.dirname(__file__), '..', '..')
if erp_path not in sys.path:
    sys.path.insert(0, erp_path)

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from erp.models.base import BaseModel
from erp.models.recordset import RecordSet
from erp.fields import Char, Integer


class TestRecordSetModel(BaseModel):
    """Test model for RecordSet integration testing"""
    _name = 'test.recordset.model'
    _description = 'Test RecordSet Model'
    _table = 'test_recordset_model'
    
    # Override default fields for testing
    id = Char(string='ID', required=True, readonly=True, default='test-id')
    name = Char(string='Name', required=True, default='Test Name')
    value = Integer(string='Value', default=10)


class TestBaseModelRecordSet:
    """Test cases for BaseModel RecordSet integration"""

    def setup_method(self):
        """Set up test case"""
        self.model_class = TestRecordSetModel

    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_search_returns_recordset(self, mock_get_db):
        """Test that search() returns a RecordSet"""
        # Mock database response
        mock_db = AsyncMock()
        mock_db.fetch.return_value = [
            {'id': 'test-1', 'name': 'Record 1', 'value': 10},
            {'id': 'test-2', 'name': 'Record 2', 'value': 20},
            {'id': 'test-3', 'name': 'Record 3', 'value': 30}
        ]
        mock_get_db.return_value = mock_db
        
        # Call search method
        result = await self.model_class.search([])
        
        # Verify result is a RecordSet
        assert isinstance(result, RecordSet)
        assert len(result) == 3

        # Verify RecordSet contains correct data
        assert result.mapped('name') == ['Record 1', 'Record 2', 'Record 3']
        assert result.mapped('value') == [10, 20, 30]
        assert result.ids() == ['test-1', 'test-2', 'test-3']
    
    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_search_empty_result_returns_empty_recordset(self, mock_get_db):
        """Test that search() returns empty RecordSet when no records found"""
        # Mock database response with empty result
        mock_db = AsyncMock()
        mock_db.fetch.return_value = []
        mock_get_db.return_value = mock_db
        
        # Call search method
        result = await self.model_class.search([('name', '=', 'nonexistent')])
        
        # Verify result is an empty RecordSet
        assert isinstance(result, RecordSet)
        assert len(result) == 0
        assert not result
        assert result.ids() == []
    
    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_search_single_record_allows_direct_access(self, mock_get_db):
        """Test that search() with single result allows direct attribute access"""
        # Mock database response with single record
        mock_db = AsyncMock()
        mock_db.fetch.return_value = [
            {'id': 'test-1', 'name': 'Single Record', 'value': 42}
        ]
        mock_get_db.return_value = mock_db
        
        # Call search method with limit=1
        result = await self.model_class.search([], limit=1)
        
        # Verify result is a RecordSet with one record
        assert isinstance(result, RecordSet)
        assert len(result) == 1

        # Verify direct attribute access works
        assert result.name == 'Single Record'
        assert result.value == 42
        assert result.id == 'test-1'
    
    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_browse_returns_recordset(self, mock_get_db):
        """Test that browse() returns a RecordSet"""
        # Mock database response
        mock_db = AsyncMock()
        mock_db.fetch.return_value = [
            {'id': 'test-1', 'name': 'Record 1', 'value': 10},
            {'id': 'test-2', 'name': 'Record 2', 'value': 20}
        ]
        mock_get_db.return_value = mock_db
        
        # Call browse method
        result = await self.model_class.browse(['test-1', 'test-2'])
        
        # Verify result is a RecordSet
        assert isinstance(result, RecordSet)
        assert len(result) == 2
        assert result.mapped('name') == ['Record 1', 'Record 2']
    
    @pytest.mark.asyncio
    async def test_browse_empty_ids_returns_empty_recordset(self):
        """Test that browse() with empty IDs returns empty RecordSet"""
        # Call browse method with empty list
        result = await self.model_class.browse([])
        
        # Verify result is an empty RecordSet
        assert isinstance(result, RecordSet)
        assert len(result) == 0
        assert not result
    
    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_browse_single_id_string(self, mock_get_db):
        """Test that browse() with single ID string works correctly"""
        # Mock database response
        mock_db = AsyncMock()
        mock_db.fetch.return_value = [
            {'id': 'test-1', 'name': 'Single Record', 'value': 100}
        ]
        mock_get_db.return_value = mock_db
        
        # Call browse method with single ID string
        result = await self.model_class.browse('test-1')
        
        # Verify result is a RecordSet with one record
        assert isinstance(result, RecordSet)
        assert len(result) == 1
        assert result.name == 'Single Record'
        assert result.value == 100
    
    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_recordset_write_operation(self, mock_get_db):
        """Test RecordSet write operation"""
        # Mock database
        mock_db = AsyncMock()
        mock_db.fetch.return_value = [
            {'id': 'test-1', 'name': 'Record 1', 'value': 10},
            {'id': 'test-2', 'name': 'Record 2', 'value': 20}
        ]
        mock_db.update.return_value = True
        mock_get_db.return_value = mock_db
        
        # Get recordset
        recordset = await self.model_class.search([])
        
        # Test write operation
        await recordset.write({'value': 999})
        
        # Verify update was called for each record
        assert mock_db.update.call_count == 2
    
    @patch('erp.models.base.DatabaseRegistry.get_current_database')
    @pytest.mark.asyncio
    async def test_recordset_unlink_operation(self, mock_get_db):
        """Test RecordSet unlink operation"""
        # Mock database
        mock_db = AsyncMock()
        mock_db.fetch.return_value = [
            {'id': 'test-1', 'name': 'Record 1', 'value': 10},
            {'id': 'test-2', 'name': 'Record 2', 'value': 20}
        ]
        mock_db.delete.return_value = True
        mock_get_db.return_value = mock_db
        
        # Get recordset
        recordset = await self.model_class.search([])
        
        # Test unlink operation
        await recordset.unlink()
        
        # Verify delete was called for each record
        assert mock_db.delete.call_count == 2


