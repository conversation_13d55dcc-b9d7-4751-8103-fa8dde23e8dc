<?xml version="1.0" encoding="utf-8"?>
<templates id="template_database_list" xml:space="preserve">

    <!-- Database List Page Template -->
    <t t-name="database_list.html">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <link rel="stylesheet" href="/static/css/database_list.css"/>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    h1 {
                        color: #333;
                        text-align: center;
                        margin-bottom: 30px;
                    }
                    #database-list {
                        margin-top: 20px;
                    }
                    .loading {
                        text-align: center;
                        color: #666;
                        font-style: italic;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1 t-esc="title"/>
                    
                    <t t-if="config.is_multi_db_mode">
                        <p>Select a database to continue:</p>
                    </t>
                    <t t-else="">
                        <p>Single database mode</p>
                    </t>
                    
                    <div id="database-list">
                        <div class="loading">Loading databases...</div>
                    </div>
                    
                    <div style="margin-top: 30px; text-align: center;">
                        <small style="color: #666;">
                            ERP System v1.0 - 
                            <span t-esc="'Multi-DB' if config.is_multi_db_mode else 'Single-DB'"/> Mode
                        </small>
                    </div>
                </div>
                
                <script>
                    // Load database list via API
                    async function loadDatabases() {
                        try {
                            const response = await fetch('/api/databases');
                            const result = await response.json();
                            
                            const container = document.getElementById('database-list');
                            
                            if (result.success &amp;&amp; result.data.length > 0) {
                                container.innerHTML = result.data.map(function(db) {
                                    return '&lt;div style="border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 4px; cursor: pointer; transition: background-color 0.2s;" ' +
                                           'onclick="selectDatabase(\'' + db.name + '\')" ' +
                                           'onmouseover="this.style.backgroundColor=\'#f0f0f0\'" ' +
                                           'onmouseout="this.style.backgroundColor=\'white\'"&gt;' +
                                        '&lt;h3 style="margin: 0 0 10px 0; color: #333;"&gt;' + db.name + '&lt;/h3&gt;' +
                                        '&lt;p style="margin: 5px 0; color: #666;"&gt;Size: ' + db.size + '&lt;/p&gt;' +
                                        '&lt;p style="margin: 5px 0; color: #666;"&gt;Status: ' + db.status + '&lt;/p&gt;' +
                                    '&lt;/div&gt;';
                                }).join('');
                            } else {
                                container.innerHTML = '<p style="text-align: center; color: #666;">No databases found</p>';
                            }
                        } catch (error) {
                            console.error('Error loading databases:', error);
                            document.getElementById('database-list').innerHTML = 
                                '<p style="text-align: center; color: #d32f2f;">Error loading databases</p>';
                        }
                    }
                    
                    function selectDatabase(dbName) {
                        window.location.href = '/app?db=' + encodeURIComponent(dbName);
                    }
                    
                    // Load databases when page loads
                    document.addEventListener('DOMContentLoaded', loadDatabases);
                </script>
            </body>
        </html>
    </t>

</templates>
