<?xml version="1.0" encoding="utf-8"?>
<templates id="template_demo" xml:space="preserve">

    <!-- Demo Template showing various features -->
    <t t-name="demo.template">
        <html>
            <head>
                <title>Template Engine Demo</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
                    .highlight { background-color: #ffffcc; }
                    .error { color: red; }
                    .success { color: green; }
                    table { border-collapse: collapse; width: 100%; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; }
                </style>
            </head>
            <body>
                <h1>Template Engine Demo</h1>
                
                <!-- Variable Setting and Display -->
                <div class="section">
                    <h2>Variables and Expressions</h2>
                    <t t-set="company_name" t-value="'Demo Company Inc.'"/>
                    <t t-set="current_year" t-value="2024"/>
                    
                    <p>Company: <strong t-esc="company_name"/></p>
                    <p>Year: <span t-esc="current_year"/></p>
                    <p>Next Year: <span t-esc="current_year + 1"/></p>
                </div>
                
                <!-- Conditional Rendering -->
                <div class="section">
                    <h2>Conditional Rendering</h2>
                    <t t-set="user_role" t-value="'admin'"/>
                    <t t-set="user_score" t-value="85"/>
                    
                    <t t-if="user_role == 'admin'">
                        <p class="success">✓ Admin access granted</p>
                    </t>
                    <t t-elif="user_role == 'user'">
                        <p>Standard user access</p>
                    </t>
                    <t t-else="">
                        <p class="error">✗ Access denied</p>
                    </t>
                    
                    <t t-if="user_score >= 90">
                        <p class="success">Excellent performance!</p>
                    </t>
                    <t t-elif="user_score >= 70">
                        <p>Good performance</p>
                    </t>
                    <t t-else="">
                        <p class="error">Needs improvement</p>
                    </t>
                </div>
                
                <!-- Dynamic Attributes -->
                <div class="section">
                    <h2>Dynamic Attributes</h2>
                    <t t-set="is_important" t-value="True"/>
                    <t t-set="item_count" t-value="5"/>
                    
                    <div t-att-class="'highlight' if is_important else 'normal'">
                        This div has dynamic class based on importance
                    </div>
                    
                    <p t-att-style="'color: red;' if item_count > 10 else 'color: blue;'">
                        This text color depends on item count (<span t-esc="item_count"/>)
                    </p>
                </div>
                
                <!-- Loops and Lists -->
                <div class="section">
                    <h2>Loops and Iteration</h2>
                    <t t-set="items" t-value="[{'name': 'Product A', 'price': 100, 'stock': 5}, {'name': 'Product B', 'price': 200, 'stock': 0}, {'name': 'Product C', 'price': 150, 'stock': 10}]"/>
                    
                    <h3>Product List:</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Product Name</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="items" t-as="product">
                                <tr t-att-class="'error' if product['stock'] == 0 else ''">
                                    <td t-esc="loop.index"/>
                                    <td t-esc="product['name']"/>
                                    <td>$<span t-esc="product['price']"/></td>
                                    <td t-esc="product['stock']"/>
                                    <td>
                                        <span t-att-class="'success' if product['stock'] > 0 else 'error'"
                                              t-esc="'In Stock' if product['stock'] > 0 else 'Out of Stock'"/>
                                    </td>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                </div>
                
                <!-- Raw HTML vs Escaped -->
                <div class="section">
                    <h2>Raw HTML vs Escaped Content</h2>
                    <t t-set="html_content" t-value="'&lt;strong&gt;Bold Text&lt;/strong&gt; &amp; special chars'"/>
                    <t t-set="safe_html" t-value="'&lt;em&gt;Italic Text&lt;/em&gt;'"/>
                    
                    <p>Escaped content: <span t-esc="html_content"/></p>
                    <p>Raw content: <span t-raw="safe_html"/></p>
                </div>
                
                <!-- Nested Loops -->
                <div class="section">
                    <h2>Nested Loops</h2>
                    <t t-set="categories" t-value="[{'name': 'Electronics', 'items': ['Phone', 'Laptop']}, {'name': 'Books', 'items': ['Novel', 'Textbook', 'Magazine']}]"/>
                    
                    <t t-foreach="categories" t-as="category">
                        <h4 t-esc="category['name']"/>
                        <ul>
                            <t t-foreach="category['items']" t-as="item">
                                <li t-esc="item"/>
                            </t>
                        </ul>
                    </t>
                </div>
                
                <!-- Ignored Content -->
                <t t-ignore="true">
                    <div class="section">
                        <h2>This section is ignored</h2>
                        <p>This content will not appear in the output</p>
                    </div>
                </t>
                
                <!-- Footer -->
                <div style="margin-top: 40px; text-align: center; color: #666; border-top: 1px solid #ddd; padding-top: 20px;">
                    <small>Generated by ERP Template Engine</small>
                </div>
            </body>
        </html>
    </t>

</templates>
