{"tests/test_schema_utils.py::TestModel": true, "tests/test_schema_utils.py::TestSchemaGenerator": true, "tests/test_schema_utils.py::TestSchemaComparator": true, "tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_discovery": true, "tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_dependency_resolution": true, "tests/integration/test_addon_loading.py::TestAddonLoading::test_circular_dependency_detection": true, "tests/integration/test_addon_loading.py::TestAddonLoading::test_missing_dependency_handling": true, "tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_loading_with_models": true, "tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_uninstallable_flag": true, "tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_auto_install_flag": true, "tests/unit/test_config.py::TestConfig::test_invalid_config_file": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_dependency_checking": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_circular_dependency_detection": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_install_order_calculation": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_installation": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_installation_with_dependencies": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_uninstallation": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_uninstallation_with_dependents": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_upgrade": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_state_persistence": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_dependency_tree": true, "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_integrity_validation": true, "addons/base/tests/test_ir_module.py::TestIrModuleModule::test_module_search_by_category": true, "tests/unit/test_domain_filter.py": true}