"""
Field definitions for ERP models - Odoo-like field system
"""
import re
from datetime import datetime, date
from decimal import Decimal


class FieldValidationError(Exception):
    """Exception raised when field validation fails"""
    pass


class Field:
    """Base field class"""

    def __init__(self, string=None, required=False, readonly=False, default=None,
                 help=None, index=False, store=True, compute=None, depends=None,
                 domain=None, context=None, **kwargs):
        self.string = string
        self.required = required
        self.readonly = readonly
        self.default = default
        self.help = help
        self.index = index
        self.store = store
        self.compute = compute
        self.depends = depends or []
        self.domain = domain or []
        self.context = context or {}
        self.kwargs = kwargs

    def get_default_value(self):
        """Get the default value for this field"""
        if callable(self.default):
            return self.default()
        return self.default

    def get_sql_type(self):
        """Get SQL type for this field"""
        return "TEXT"

    def validate(self, value):
        """Validate field value"""
        if value is None:
            if self.required:
                raise FieldValidationError(f"Field '{self.string or 'unknown'}' is required")
            return None

        # Perform type-specific validation
        return self._validate_value(value)

    def _validate_value(self, value):
        """Override in subclasses for type-specific validation"""
        return value

    def convert_to_cache(self, value):
        """Convert value for caching"""
        return value

    def convert_to_record(self, value):
        """Convert cached value back to record value"""
        return value

    def convert_to_write(self, value):
        """Convert value for database write"""
        return value

    def convert_to_read(self, value):
        """Convert database value for reading"""
        return value


class Char(Field):
    """Character field"""

    def __init__(self, size=None, translate=False, **kwargs):
        super().__init__(**kwargs)
        self.size = size
        self.translate = translate

    def get_sql_type(self):
        if self.size:
            return f"VARCHAR({self.size})"
        return "TEXT"

    def _validate_value(self, value):
        """Validate character field value"""
        if not isinstance(value, str):
            try:
                value = str(value)
            except (ValueError, TypeError):
                raise FieldValidationError(f"Cannot convert {type(value).__name__} to string")

        if self.size and len(value) > self.size:
            raise FieldValidationError(f"String too long: {len(value)} > {self.size}")

        return value


class Text(Field):
    """Text field for longer content"""

    def __init__(self, translate=False, **kwargs):
        super().__init__(**kwargs)
        self.translate = translate

    def get_sql_type(self):
        return "TEXT"

    def _validate_value(self, value):
        """Validate text field value"""
        if not isinstance(value, str):
            try:
                value = str(value)
            except (ValueError, TypeError):
                raise FieldValidationError(f"Cannot convert {type(value).__name__} to string")
        return value


class Integer(Field):
    """Integer field"""

    def __init__(self, min_value=None, max_value=None, **kwargs):
        super().__init__(**kwargs)
        self.min_value = min_value
        self.max_value = max_value

    def get_sql_type(self):
        return "INTEGER"

    def _validate_value(self, value):
        """Validate integer field value"""
        if not isinstance(value, int):
            try:
                value = int(value)
            except (ValueError, TypeError):
                raise FieldValidationError(f"Cannot convert {type(value).__name__} to integer")

        if self.min_value is not None and value < self.min_value:
            raise FieldValidationError(f"Value {value} is less than minimum {self.min_value}")

        if self.max_value is not None and value > self.max_value:
            raise FieldValidationError(f"Value {value} is greater than maximum {self.max_value}")

        return value


class Float(Field):
    """Float field"""

    def __init__(self, digits=None, min_value=None, max_value=None, **kwargs):
        super().__init__(**kwargs)
        self.digits = digits  # (precision, scale) tuple
        self.min_value = min_value
        self.max_value = max_value

    def get_sql_type(self):
        if self.digits:
            precision, scale = self.digits
            return f"NUMERIC({precision},{scale})"
        return "REAL"

    def _validate_value(self, value):
        """Validate float field value"""
        if not isinstance(value, (int, float, Decimal)):
            try:
                value = float(value)
            except (ValueError, TypeError):
                raise FieldValidationError(f"Cannot convert {type(value).__name__} to float")

        if self.min_value is not None and value < self.min_value:
            raise FieldValidationError(f"Value {value} is less than minimum {self.min_value}")

        if self.max_value is not None and value > self.max_value:
            raise FieldValidationError(f"Value {value} is greater than maximum {self.max_value}")

        # Apply precision if specified
        if self.digits:
            _, scale = self.digits
            value = round(float(value), scale)

        return value


class Boolean(Field):
    """Boolean field"""

    def get_sql_type(self):
        return "BOOLEAN"

    def _validate_value(self, value):
        """Validate boolean field value"""
        if isinstance(value, bool):
            return value

        # Convert common truthy/falsy values
        if isinstance(value, str):
            value_lower = value.lower()
            if value_lower in ('true', '1', 'yes', 'on'):
                return True
            elif value_lower in ('false', '0', 'no', 'off', ''):
                return False

        # Convert numeric values
        if isinstance(value, (int, float)):
            return bool(value)

        raise FieldValidationError(f"Cannot convert {type(value).__name__} to boolean")


class Date(Field):
    """Date field"""

    def get_sql_type(self):
        return "DATE"

    def _validate_value(self, value):
        """Validate date field value"""
        if isinstance(value, datetime):
            return value.date()

        if isinstance(value, date):
            return value

        if isinstance(value, str):
            try:
                # Try common date formats
                for fmt in ('%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S'):
                    try:
                        parsed = datetime.strptime(value, fmt)
                        return parsed.date()
                    except ValueError:
                        continue
                raise ValueError("No matching date format found")
            except ValueError:
                raise FieldValidationError(f"Cannot parse date from string: {value}")

        raise FieldValidationError(f"Cannot convert {type(value).__name__} to date")


class Datetime(Field):
    """Datetime field"""

    def __init__(self, **kwargs):
        if 'default' not in kwargs:
            kwargs['default'] = lambda: datetime.now()
        super().__init__(**kwargs)

    def get_sql_type(self):
        return "TIMESTAMP"

    def _validate_value(self, value):
        """Validate datetime field value"""
        if isinstance(value, datetime):
            return value

        if isinstance(value, date):
            return datetime.combine(value, datetime.min.time())

        if isinstance(value, str):
            try:
                # Try common datetime formats
                for fmt in ('%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f',
                           '%Y-%m-%d', '%d/%m/%Y %H:%M:%S', '%m/%d/%Y %H:%M:%S'):
                    try:
                        return datetime.strptime(value, fmt)
                    except ValueError:
                        continue
                raise ValueError("No matching datetime format found")
            except ValueError:
                raise FieldValidationError(f"Cannot parse datetime from string: {value}")

        raise FieldValidationError(f"Cannot convert {type(value).__name__} to datetime")


class Selection(Field):
    """Selection field"""

    def __init__(self, selection, **kwargs):
        super().__init__(**kwargs)
        self.selection = selection

    def get_sql_type(self):
        return "VARCHAR(255)"

    def _validate_value(self, value):
        """Validate selection field value"""
        if value is None:
            return None

        # Get valid values from selection
        valid_values = []
        if isinstance(self.selection, list):
            for item in self.selection:
                if isinstance(item, (list, tuple)) and len(item) >= 2:
                    valid_values.append(item[0])
                else:
                    valid_values.append(item)
        elif callable(self.selection):
            # Dynamic selection - call the function
            selection_result = self.selection()
            for item in selection_result:
                if isinstance(item, (list, tuple)) and len(item) >= 2:
                    valid_values.append(item[0])
                else:
                    valid_values.append(item)

        if value not in valid_values:
            raise FieldValidationError(f"Invalid selection value: {value}. Valid values: {valid_values}")

        return value

    def get_selection_items(self):
        """Get selection items as list of (value, label) tuples"""
        if isinstance(self.selection, list):
            items = []
            for item in self.selection:
                if isinstance(item, (list, tuple)) and len(item) >= 2:
                    items.append((item[0], item[1]))
                else:
                    items.append((item, item))
            return items
        elif callable(self.selection):
            selection_result = self.selection()
            items = []
            for item in selection_result:
                if isinstance(item, (list, tuple)) and len(item) >= 2:
                    items.append((item[0], item[1]))
                else:
                    items.append((item, item))
            return items
        return []


# Additional specialized fields

class Binary(Field):
    """Binary field for file storage"""

    def __init__(self, attachment=True, **kwargs):
        super().__init__(**kwargs)
        self.attachment = attachment

    def get_sql_type(self):
        if self.attachment:
            return "VARCHAR(255)"  # Store attachment reference
        return "BYTEA"  # Store binary data directly

    def _validate_value(self, value):
        """Validate binary field value"""
        if value is None:
            return None

        if isinstance(value, (bytes, bytearray)):
            return value

        if isinstance(value, str):
            # Assume base64 encoded
            import base64
            try:
                return base64.b64decode(value)
            except Exception:
                raise FieldValidationError("Invalid base64 encoded binary data")

        raise FieldValidationError(f"Cannot convert {type(value).__name__} to binary")


class Json(Field):
    """JSON field for structured data"""

    def get_sql_type(self):
        return "JSONB"

    def _validate_value(self, value):
        """Validate JSON field value"""
        if value is None:
            return None

        # Ensure value is JSON serializable
        import json
        try:
            json.dumps(value)
            return value
        except (TypeError, ValueError) as e:
            raise FieldValidationError(f"Value is not JSON serializable: {e}")


class Html(Text):
    """HTML field - extends Text with HTML-specific features"""

    def __init__(self, sanitize=True, **kwargs):
        super().__init__(**kwargs)
        self.sanitize = sanitize

    def _validate_value(self, value):
        """Validate HTML field value"""
        value = super()._validate_value(value)

        if self.sanitize and value:
            # Basic HTML sanitization (in a real implementation, use a proper library)
            import re
            # Remove script tags and their content
            value = re.sub(r'<script[^>]*>.*?</script>', '', value, flags=re.DOTALL | re.IGNORECASE)
            # Remove dangerous attributes
            value = re.sub(r'\s(on\w+|javascript:)[^>]*', '', value, flags=re.IGNORECASE)

        return value
