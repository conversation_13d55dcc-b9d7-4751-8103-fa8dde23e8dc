#!/usr/bin/env python3
"""
Demonstration of Odoo-style RecordSet functionality
This script shows how to use the new RecordSet features in the ERP system.
"""
import sys
import os
import asyncio

# Add ERP core to path
erp_path = os.path.join(os.path.dirname(__file__), '..')
if erp_path not in sys.path:
    sys.path.insert(0, erp_path)

from erp.models.base import BaseModel
from erp.models.recordset import RecordSet
from erp.fields import <PERSON><PERSON>, Integer, Boolean


class DemoModel(BaseModel):
    """Demo model for RecordSet demonstration"""
    _name = 'demo.model'
    _description = 'Demo Model for RecordSet'
    _table = 'demo_model'
    
    # Custom fields (id, name, createAt, updateAt are inherited)
    description = Char(string='Description', default='Demo record')
    priority = Integer(string='Priority', default=1)
    active = Boolean(string='Active', default=True)


def demo_recordset_creation():
    """Demonstrate RecordSet creation and basic operations"""
    print("=== RecordSet Creation Demo ===")
    
    # Create some demo records
    record1 = DemoModel.create_sync({
        'id': 'demo-1',
        'name': 'First Record',
        'description': 'This is the first demo record',
        'priority': 1,
        'active': True
    })
    
    record2 = DemoModel.create_sync({
        'id': 'demo-2',
        'name': 'Second Record',
        'description': 'This is the second demo record',
        'priority': 2,
        'active': True
    })
    
    record3 = DemoModel.create_sync({
        'id': 'demo-3',
        'name': 'Third Record',
        'description': 'This is the third demo record',
        'priority': 3,
        'active': False
    })
    
    # Create a RecordSet
    recordset = RecordSet(DemoModel, [record1, record2, record3])
    
    print(f"Created RecordSet with {len(recordset)} records")
    print(f"RecordSet representation: {recordset}")
    print(f"Record IDs: {recordset.ids()}")
    print()
    
    return recordset


def demo_single_record_access(recordset):
    """Demonstrate single record attribute access"""
    print("=== Single Record Access Demo ===")
    
    # Get a single record
    single_record = recordset[0:1]  # This creates a new RecordSet with one record
    print(f"Single record: {single_record}")
    print(f"Direct attribute access - Name: {single_record.name}")
    print(f"Direct attribute access - Description: {single_record.description}")
    print(f"Direct attribute access - Priority: {single_record.priority}")
    print()


def demo_multiple_record_operations(recordset):
    """Demonstrate operations on multiple records"""
    print("=== Multiple Record Operations Demo ===")
    
    # Mapped operations
    names = recordset.mapped('name')
    print(f"All names: {names}")
    
    priorities = recordset.mapped('priority')
    print(f"All priorities: {priorities}")
    
    descriptions = recordset.mapped('description')
    print(f"All descriptions: {descriptions}")
    print()


def demo_filtering_and_sorting(recordset):
    """Demonstrate filtering and sorting operations"""
    print("=== Filtering and Sorting Demo ===")
    
    # Filter active records
    active_records = recordset.filtered(lambda r: r.active)
    print(f"Active records: {len(active_records)}")
    print(f"Active record names: {active_records.mapped('name')}")
    
    # Filter by priority
    high_priority = recordset.filtered(lambda r: r.priority > 1)
    print(f"High priority records: {len(high_priority)}")
    print(f"High priority names: {high_priority.mapped('name')}")
    
    # Sort by priority (descending)
    sorted_by_priority = recordset.sorted(key=lambda r: r.priority, reverse=True)
    print(f"Sorted by priority (desc): {sorted_by_priority.mapped('name')}")
    
    # Sort by name
    sorted_by_name = recordset.sorted(key=lambda r: r.name)
    print(f"Sorted by name: {sorted_by_name.mapped('name')}")
    print()


def demo_recordset_slicing(recordset):
    """Demonstrate RecordSet slicing operations"""
    print("=== RecordSet Slicing Demo ===")
    
    # Get first two records
    first_two = recordset[:2]
    print(f"First two records: {first_two.mapped('name')}")
    
    # Get last record
    last_record = recordset[-1:]
    print(f"Last record: {last_record.mapped('name')}")
    
    # Get middle record
    middle_record = recordset[1:2]
    print(f"Middle record: {middle_record.mapped('name')}")
    print()


def demo_ensure_one():
    """Demonstrate ensure_one functionality"""
    print("=== Ensure One Demo ===")
    
    # Create a single record RecordSet
    single_record = DemoModel.create_sync({
        'id': 'single-1',
        'name': 'Single Record',
        'description': 'This is a single record for ensure_one demo',
        'priority': 5
    })
    
    single_recordset = RecordSet(DemoModel, [single_record])
    
    try:
        ensured_record = single_recordset.ensure_one()
        print(f"ensure_one() success: {ensured_record.name}")
    except ValueError as e:
        print(f"ensure_one() error: {e}")
    
    # Try with empty RecordSet
    empty_recordset = RecordSet(DemoModel, [])
    try:
        empty_recordset.ensure_one()
    except ValueError as e:
        print(f"ensure_one() on empty RecordSet: {e}")
    
    # Try with multiple records
    multiple_records = RecordSet(DemoModel, [single_record, single_record])
    try:
        multiple_records.ensure_one()
    except ValueError as e:
        print(f"ensure_one() on multiple records: {e}")
    print()


def demo_attribute_errors(recordset):
    """Demonstrate attribute access error handling"""
    print("=== Attribute Access Error Demo ===")
    
    # Try to access attribute on multiple records
    try:
        _ = recordset.name  # This should fail
    except AttributeError as e:
        print(f"Multiple records attribute access error: {e}")
    
    # Try to access attribute on empty RecordSet
    empty_recordset = RecordSet(DemoModel, [])
    try:
        _ = empty_recordset.name  # This should fail
    except AttributeError as e:
        print(f"Empty RecordSet attribute access error: {e}")
    print()


async def demo_async_operations():
    """Demonstrate async operations (simulated)"""
    print("=== Async Operations Demo (Simulated) ===")
    
    # Note: These operations would normally interact with the database
    # For demo purposes, we'll show the API without actual database calls
    
    print("Example usage with async operations:")
    print("# Search for records (returns RecordSet)")
    print("recordset = await DemoModel.search([('active', '=', True)])")
    print()
    
    print("# Browse specific records (returns RecordSet)")
    print("recordset = await DemoModel.browse(['demo-1', 'demo-2'])")
    print()
    
    print("# Single record direct access")
    print("single_record = await DemoModel.search([('name', '=', 'First Record')], limit=1)")
    print("print(single_record.name)  # Direct access works for single record")
    print()
    
    print("# Bulk operations on RecordSet")
    print("await recordset.write({'priority': 10})  # Update all records")
    print("await recordset.unlink()  # Delete all records")
    print()


def main():
    """Main demonstration function"""
    print("Odoo-style RecordSet Demonstration")
    print("=" * 50)
    print()
    
    # Create demo data
    recordset = demo_recordset_creation()
    
    # Demonstrate various RecordSet features
    demo_single_record_access(recordset)
    demo_multiple_record_operations(recordset)
    demo_filtering_and_sorting(recordset)
    demo_recordset_slicing(recordset)
    demo_ensure_one()
    demo_attribute_errors(recordset)
    
    # Show async operations
    asyncio.run(demo_async_operations())
    
    print("=" * 50)
    print("RecordSet demonstration completed!")
    print()
    print("Key benefits of RecordSet:")
    print("- Direct attribute access on single records")
    print("- Convenient operations like mapped(), filtered(), sorted()")
    print("- Slicing support with new RecordSet instances")
    print("- Bulk operations (write, unlink)")
    print("- Odoo-compatible API")


if __name__ == '__main__':
    main()
