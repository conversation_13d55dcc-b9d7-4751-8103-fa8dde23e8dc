<?xml version="1.0" encoding="utf-8"?>
<templates id="template_app_home" xml:space="preserve">

    <!-- App Home Page Template -->
    <t t-name="app_home.html">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 0;
                        background-color: #f5f5f5;
                    }
                    .header {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 20px;
                        text-align: center;
                    }
                    .container {
                        max-width: 1200px;
                        margin: 20px auto;
                        padding: 0 20px;
                    }
                    .card {
                        background: white;
                        border-radius: 8px;
                        padding: 20px;
                        margin: 20px 0;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    .status-info {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                        gap: 20px;
                        margin: 20px 0;
                    }
                    .status-card {
                        background: white;
                        border-radius: 8px;
                        padding: 20px;
                        text-align: center;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    .status-value {
                        font-size: 2em;
                        font-weight: bold;
                        color: #667eea;
                        margin: 10px 0;
                    }
                    .status-label {
                        color: #666;
                        font-size: 0.9em;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1 t-esc="title"/>
                    <p t-esc="subtitle"/>
                </div>
                
                <div class="container">
                    <!-- Database Info Card -->
                    <div class="card">
                        <h2>Database Information</h2>
                        <p><strong>Database:</strong> <span t-esc="database_name"/></p>
                        <p><strong>Mode:</strong> <span t-esc="mode"/></p>
                    </div>
                    
                    <!-- System Status -->
                    <div class="card">
                        <h2>System Status</h2>
                        <div class="status-info">
                            <div class="status-card">
                                <div class="status-value" t-esc="addon_count or 0"/>
                                <div class="status-label">Loaded Addons</div>
                            </div>
                            <div class="status-card">
                                <div class="status-value" t-esc="'✓' if database_connected else '✗'"/>
                                <div class="status-label">Database Connection</div>
                            </div>
                            <div class="status-card">
                                <div class="status-value" t-esc="server_status or 'Unknown'"/>
                                <div class="status-label">Server Status</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="card">
                        <h2>Quick Actions</h2>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <button onclick="window.location.href='/api/databases'" 
                                    style="padding: 15px; border: none; background: #667eea; color: white; border-radius: 4px; cursor: pointer;">
                                View Databases
                            </button>
                            <button onclick="window.location.href='/addons'" 
                                    style="padding: 15px; border: none; background: #764ba2; color: white; border-radius: 4px; cursor: pointer;">
                                View Addons
                            </button>
                            <button onclick="window.location.href='/health'" 
                                    style="padding: 15px; border: none; background: #28a745; color: white; border-radius: 4px; cursor: pointer;">
                                Health Check
                            </button>
                        </div>
                    </div>
                    
                    <!-- Conditional Content -->
                    <t t-if="show_debug_info">
                        <div class="card">
                            <h2>Debug Information</h2>
                            <pre t-esc="debug_info"/>
                        </div>
                    </t>
                    
                    <!-- Footer -->
                    <div style="text-align: center; margin: 40px 0; color: #666;">
                        <small>
                            ERP System v1.0 - Powered by FastAPI and Custom Template Engine
                        </small>
                    </div>
                </div>
            </body>
        </html>
    </t>

</templates>
