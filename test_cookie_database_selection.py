#!/usr/bin/env python3
"""
Test script for cookie-based database selection functionality
"""
import asyncio
import aiohttp
import json
from urllib.parse import urlencode

async def test_cookie_database_selection():
    """Test the cookie-based database selection functionality"""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        print("🧪 Testing Cookie-Based Database Selection")
        print("=" * 50)
        
        # Test 1: Get available databases
        print("\n1. Getting available databases...")
        async with session.get(f"{base_url}/api/databases") as response:
            if response.status == 200:
                data = await response.json()
                databases = data.get('data', [])
                print(f"   ✅ Found {len(databases)} databases: {[db['name'] for db in databases]}")
                
                if not databases:
                    print("   ❌ No databases available for testing")
                    return
                    
                test_db = databases[0]['name']
            else:
                print(f"   ❌ Failed to get databases: {response.status}")
                return
        
        # Test 2: Set database via query parameter (should set cookie)
        print(f"\n2. Setting database '{test_db}' via query parameter...")
        async with session.get(f"{base_url}/app?db={test_db}", allow_redirects=False) as response:
            print(f"   Status: {response.status}")
            
            # Check if cookie was set
            cookies = session.cookie_jar.filter_cookies(base_url)
            erp_db_cookie = cookies.get('erp_database')
            
            if erp_db_cookie:
                print(f"   ✅ Cookie set: erp_database={erp_db_cookie.value}")
            else:
                print("   ❌ Cookie not set")
        
        # Test 3: Access app without query parameter (should use cookie)
        print(f"\n3. Accessing app without query parameter (should use cookie)...")
        async with session.get(f"{base_url}/app", allow_redirects=False) as response:
            print(f"   Status: {response.status}")
            
            if response.status == 200:
                print("   ✅ Successfully accessed app using cookie")
            elif response.status in [301, 302, 307, 308]:
                location = response.headers.get('Location', '')
                print(f"   ℹ️  Redirected to: {location}")
            else:
                print(f"   ❌ Unexpected response: {response.status}")
        
        # Test 4: Test database list page with current database highlighting
        print(f"\n4. Testing database list page...")
        async with session.get(f"{base_url}/app/database/list") as response:
            if response.status == 200:
                content = await response.text()
                if 'database-list.js' in content:
                    print("   ✅ Database list page loaded successfully")
                    print("   ✅ JavaScript for current database highlighting included")
                else:
                    print("   ⚠️  Database list page loaded but JavaScript may be missing")
            else:
                print(f"   ❌ Failed to load database list page: {response.status}")
        
        # Test 5: Test header-based database selection (should override cookie)
        print(f"\n5. Testing header-based database selection (should override cookie)...")
        if len(databases) > 1:
            override_db = databases[1]['name']
            headers = {'X-Database': override_db}
            
            async with session.get(f"{base_url}/app", headers=headers, allow_redirects=False) as response:
                print(f"   Status: {response.status}")
                print(f"   ✅ Header-based selection should override cookie")
        else:
            print("   ⚠️  Only one database available, cannot test header override")
        
        print(f"\n🎉 Cookie-based database selection test completed!")
        print("\nTo manually test:")
        print(f"1. Visit: {base_url}/app/database/list")
        print("2. Click on a database to select it")
        print("3. Navigate to other pages - the selection should persist")
        print("4. Check browser cookies to see 'erp_database' cookie")

if __name__ == "__main__":
    print("Starting cookie-based database selection test...")
    print("Make sure the ERP server is running on http://localhost:8000")
    print()
    
    try:
        asyncio.run(test_cookie_database_selection())
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")