"""
Database connection and management using asyncpg
"""
import asyncpg
from typing import Dict, Any, Optional, List, Union
from contextlib import asynccontextmanager
from ..config import config


class DatabaseManager:
    """Database connection manager with connection pooling"""
    
    def __init__(self, db_name: Optional[str] = None):
        self.db_name = db_name or config.get('options', 'db_name')
        self._pool: Optional[asyncpg.Pool] = None
    
    async def create_pool(self):
        """Create connection pool"""
        if self._pool is None:
            db_config = config.db_config.copy()
            pool_config = config.db_pool_config
            
            if self.db_name:
                db_config['database'] = self.db_name
            
            try:
                self._pool = await asyncpg.create_pool(
                    host=db_config['host'],
                    port=db_config['port'],
                    user=db_config['user'],
                    password=db_config['password'],
                    database=db_config['database'],
                    min_size=pool_config['min_size'],
                    max_size=pool_config['max_size']
                )
            except Exception as e:
                print(f"Database pool creation error: {e}")
                raise
    
    async def close_pool(self):
        """Close connection pool"""
        if self._pool:
            await self._pool.close()
            self._pool = None
    
    @asynccontextmanager
    async def acquire_connection(self):
        """Acquire connection from pool"""
        if not self._pool:
            await self.create_pool()
        
        async with self._pool.acquire() as connection:
            yield connection
    
    async def execute(self, query: str, *args) -> str:
        """Execute a query and return status"""
        async with self.acquire_connection() as conn:
            return await conn.execute(query, *args)
    
    async def fetch(self, query: str, *args) -> List[asyncpg.Record]:
        """Fetch multiple rows"""
        async with self.acquire_connection() as conn:
            return await conn.fetch(query, *args)
    
    async def fetchrow(self, query: str, *args) -> Optional[asyncpg.Record]:
        """Fetch single row"""
        async with self.acquire_connection() as conn:
            return await conn.fetchrow(query, *args)
    
    async def fetchval(self, query: str, *args) -> Any:
        """Fetch single value"""
        async with self.acquire_connection() as conn:
            return await conn.fetchval(query, *args)
    
    async def insert(self, table: str, data: Dict[str, Any]) -> Optional[str]:
        """Insert a record and return the ID"""
        if not data:
            return None
        
        columns = list(data.keys())
        values = list(data.values())
        placeholders = [f"${i+1}" for i in range(len(values))]
        
        query = f"""
            INSERT INTO {table} ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            RETURNING id
        """
        
        result = await self.fetchval(query, *values)
        return str(result) if result else None
    
    async def update(self, table: str, record_id: str, data: Dict[str, Any]) -> bool:
        """Update a record"""
        if not data:
            return False
        
        set_clauses = []
        values = []
        
        for i, (column, value) in enumerate(data.items(), 1):
            set_clauses.append(f"{column} = ${i}")
            values.append(value)
        
        query = f"""
            UPDATE {table}
            SET {', '.join(set_clauses)}
            WHERE id = ${len(values) + 1}
        """
        values.append(record_id)
        
        result = await self.execute(query, *values)
        return result.split()[-1] == '1'  # Check if one row was updated
    
    async def delete(self, table: str, record_id: str) -> bool:
        """Delete a record"""
        query = f"DELETE FROM {table} WHERE id = $1"
        result = await self.execute(query, record_id)
        return result.split()[-1] == '1'  # Check if one row was deleted
    
    async def exists(self, table: str, record_id: str) -> bool:
        """Check if a record exists"""
        query = f"SELECT 1 FROM {table} WHERE id = $1 LIMIT 1"
        result = await self.fetchval(query, record_id)
        return result is not None
    
    async def count(self, table: str, where_clause: str = "", params: List = None) -> int:
        """Count records in table"""
        query = f"SELECT COUNT(*) FROM {table}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        params = params or []
        result = await self.fetchval(query, *params)
        return result or 0
    
    async def create_table(self, table_name: str, columns: Dict[str, str]):
        """Create a table with specified columns"""
        column_definitions = []
        for column_name, column_type in columns.items():
            column_definitions.append(f"{column_name} {column_type}")
        
        query = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {', '.join(column_definitions)}
            )
        """
        
        await self.execute(query)
    
    async def drop_table(self, table_name: str):
        """Drop a table"""
        query = f"DROP TABLE IF EXISTS {table_name}"
        await self.execute(query)
    
    async def table_exists(self, table_name: str) -> bool:
        """Check if a table exists"""
        query = """
            SELECT 1 FROM information_schema.tables
            WHERE table_name = $1 AND table_schema = 'public'
            LIMIT 1
        """
        result = await self.fetchval(query, table_name)
        return result is not None
    
    async def get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """Get column information for a table"""
        query = """
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = $1 AND table_schema = 'public'
            ORDER BY ordinal_position
        """
        rows = await self.fetch(query, table_name)
        return [dict(row) for row in rows]
    
    async def begin_transaction(self):
        """Begin a transaction"""
        return await self.execute("BEGIN")
    
    async def commit_transaction(self):
        """Commit current transaction"""
        return await self.execute("COMMIT")
    
    async def rollback_transaction(self):
        """Rollback current transaction"""
        return await self.execute("ROLLBACK")
