# Domain Filter Modularity Improvement

## Overview

The domain filtering functionality in `erp/models/base.py` has been successfully refactored to improve modularity and reusability across the ERP application.

## Problem

Previously, the domain filtering logic was embedded directly in the `BaseModel.search()` method (lines 147-183), making it:
- Difficult to reuse in other parts of the application
- Hard to maintain and extend
- Tightly coupled to the database search functionality
- Not available for in-memory record filtering

## Solution

Created a modular `DomainFilter` utility class in `erp/utils/domain.py` that provides:

### 1. Centralized Domain Filtering Logic
- **File**: [`erp/utils/domain.py`](../erp/utils/domain.py)
- **Class**: `DomainFilter`
- **Purpose**: Handles all domain filtering operations in a reusable way

### 2. Key Features

#### Supported Operators
- `=` - Equals
- `!=` - Not equals  
- `>` - Greater than
- `<` - Less than
- `>=` - Greater than or equal
- `<=` - Less than or equal
- `like` - SQL LIKE pattern matching
- `ilike` - Case-insensitive LIKE
- `in` - Value in list
- `not in` - Value not in list
- `=?` - Equals or null (special case)
- `=like` - Equals or like (special case)
- `=ilike` - Equals or case-insensitive like (special case)

#### Core Methods

1. **`build_sql_where_clause(domain, param_offset=0)`**
   - Converts domain conditions to SQL WHERE clauses
   - Returns parameterized queries for security
   - Supports parameter offset for complex queries

2. **`filter_records(records, domain)`**
   - Filters in-memory record collections
   - Works with any object that has the required attributes
   - Useful for post-database filtering

3. **`validate_domain(domain)`**
   - Validates domain format and operators
   - Provides clear error messages for invalid domains
   - Ensures type safety

4. **`combine_domains(*domains)`**
   - Combines multiple domains with AND logic
   - Useful for building complex filters

5. **`domain_to_string(domain)`**
   - Converts domains to human-readable strings
   - Helpful for debugging and logging

## Implementation Changes

### 1. Updated BaseModel.search()
**File**: [`erp/models/base.py`](../erp/models/base.py)

**Before** (lines 147-183):
```python
# Add WHERE clause for domain
if domain:
    where_conditions = []
    for condition in domain:
        if len(condition) == 3:
            field, operator, value = condition
            if operator == '=':
                where_conditions.append(f"{field} = ${len(params) + 1}")
                params.append(value)
            # ... 30+ lines of operator handling
```

**After** (lines 147-152):
```python
# Add WHERE clause for domain using DomainFilter
if domain:
    where_clause, domain_params = DomainFilter.build_sql_where_clause(domain, len(params))
    if where_clause:
        query += " WHERE " + where_clause
        params.extend(domain_params)
```

### 2. Enhanced RecordSet.filtered()
**File**: [`erp/models/recordset.py`](../erp/models/recordset.py)

**Before** (lines 117-122):
```python
if callable(func_or_domain):
    filtered_records = [r for r in self._records if func_or_domain(r)]
else:
    # For now, just return self if domain is provided
    # TODO: Implement domain filtering
    filtered_records = self._records
```

**After** (lines 117-120):
```python
if callable(func_or_domain):
    filtered_records = [r for r in self._records if func_or_domain(r)]
else:
    # Use DomainFilter for domain-based filtering
    filtered_records = DomainFilter.filter_records(self._records, func_or_domain)
```

### 3. Updated Utils Package
**File**: [`erp/utils/__init__.py`](../erp/utils/__init__.py)

Added `DomainFilter` to the exports:
```python
from .domain import DomainFilter
# ... added to __all__ lists
```

## Usage Examples

### 1. Database Search (BaseModel)
```python
# Simple equality
records = await MyModel.search([('name', '=', 'John')])

# Multiple conditions
records = await MyModel.search([
    ('active', '=', True),
    ('age', '>', 25),
    ('department', 'in', ['Sales', 'Engineering'])
])
```

### 2. In-Memory Filtering (RecordSet)
```python
# Filter existing recordset
active_records = recordset.filtered([('active', '=', True)])

# Complex filtering
filtered = recordset.filtered([
    ('salary', '>=', 50000),
    ('department', '!=', 'Temp')
])
```

### 3. Direct Usage
```python
from erp.utils.domain import DomainFilter

# Generate SQL
domain = [('name', 'like', '%John%'), ('active', '=', True)]
where_clause, params = DomainFilter.build_sql_where_clause(domain)
# Result: "name LIKE $1 AND active = $2", ['%John%', True]

# Filter records
filtered_records = DomainFilter.filter_records(my_records, domain)
```

## Benefits

### 1. **Improved Modularity**
- Domain filtering logic is now centralized and reusable
- Can be used across different parts of the application
- Easier to maintain and extend

### 2. **Enhanced Functionality**
- Support for more operators (including special cases)
- Better error handling and validation
- Consistent behavior across database and in-memory filtering

### 3. **Code Reduction**
- Reduced the `BaseModel.search()` method from ~40 lines to ~6 lines
- Eliminated code duplication
- Cleaner, more readable code

### 4. **Better Testing**
- Domain filtering logic can be tested independently
- Comprehensive test suite in [`tests/unit/test_domain_filter.py`](../tests/unit/test_domain_filter.py)
- Easier to verify edge cases and error conditions

### 5. **Security**
- Parameterized queries prevent SQL injection
- Input validation ensures safe operations
- Type checking prevents runtime errors

## Files Modified

1. **Created**: [`erp/utils/domain.py`](../erp/utils/domain.py) - Main domain filter utility
2. **Modified**: [`erp/models/base.py`](../erp/models/base.py) - Updated search method
3. **Modified**: [`erp/models/recordset.py`](../erp/models/recordset.py) - Enhanced filtered method
4. **Modified**: [`erp/utils/__init__.py`](../erp/utils/__init__.py) - Added exports
5. **Created**: [`tests/unit/test_domain_filter.py`](../tests/unit/test_domain_filter.py) - Comprehensive tests
6. **Created**: [`examples/domain_filter_demo.py`](../examples/domain_filter_demo.py) - Usage examples
7. **Created**: [`examples/standalone_domain_demo.py`](../examples/standalone_domain_demo.py) - Standalone demo

## Testing

Run the standalone demo to see the domain filtering in action:
```bash
python examples/standalone_domain_demo.py
```

Run the comprehensive test suite:
```bash
python -m pytest tests/unit/test_domain_filter.py -v
```

## Future Enhancements

The modular design allows for easy future enhancements:

1. **OR Logic Support**: Add support for OR conditions between domain clauses
2. **Nested Domains**: Support for complex nested domain structures
3. **Custom Operators**: Easy addition of new operators
4. **Performance Optimization**: Caching and optimization for large datasets
5. **Integration**: Easy integration with other ERP components

## Conclusion

The domain filtering functionality has been successfully modularized, providing:
- Better code organization and maintainability
- Increased reusability across the application
- Enhanced functionality and error handling
- Improved security through parameterized queries
- Comprehensive testing and documentation

This refactoring makes the ERP system more robust and easier to extend while maintaining backward compatibility.