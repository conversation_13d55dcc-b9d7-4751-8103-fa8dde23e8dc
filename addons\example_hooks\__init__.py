"""
Example addon demonstrating lifecycle hooks
"""
import logging
from erp.addons.hooks import (
    pre_install_hook, post_install_hook,
    pre_uninstall_hook, post_uninstall_hook,
    pre_upgrade_hook, post_upgrade_hook
)

logger = logging.getLogger(__name__)


@pre_install_hook('example_hooks', priority=10)
async def pre_install(context):
    """Pre-install hook - runs before addon installation"""
    logger.info(f"PRE-INSTALL: {context.addon_name}")
    logger.info(f"Environment: {context.env}")
    
    if context.env:
        # Example: Check if required tables exist
        logger.info(f"Database: {context.env.cr.db_name}")
        logger.info(f"User: {context.env.uid}")
        logger.info(f"Context: {context.env.context}")
        
        # Example database operation
        try:
            result = await context.env.cr.fetchval(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'res_users'"
            )
            logger.info(f"Found {result} res_users tables")
        except Exception as e:
            logger.warning(f"Could not check tables: {e}")


@post_install_hook('example_hooks', priority=10)
async def post_install(context):
    """Post-install hook - runs after addon installation"""
    logger.info(f"POST-INSTALL: {context.addon_name}")
    
    if context.env:
        # Example: Create initial data
        logger.info("Creating initial data...")
        
        # Example: Insert some configuration data
        try:
            # This is just an example - in real addons you'd use models
            await context.env.cr.execute(
                "INSERT INTO ir_config_parameter (key, value) VALUES ($1, $2) ON CONFLICT (key) DO UPDATE SET value = $2",
                f"example_hooks.installed_at", "now()"
            )
            logger.info("Initial data created successfully")
        except Exception as e:
            logger.warning(f"Could not create initial data: {e}")


@pre_uninstall_hook('example_hooks', priority=10)
async def pre_uninstall(context):
    """Pre-uninstall hook - runs before addon uninstallation"""
    logger.info(f"PRE-UNINSTALL: {context.addon_name}")
    
    if context.env:
        # Example: Backup important data
        logger.info("Backing up addon data...")
        
        try:
            # Example backup operation
            result = await context.env.cr.fetchval(
                "SELECT value FROM ir_config_parameter WHERE key = $1",
                "example_hooks.installed_at"
            )
            if result:
                logger.info(f"Addon was installed at: {result}")
        except Exception as e:
            logger.warning(f"Could not backup data: {e}")


@post_uninstall_hook('example_hooks', priority=10)
async def post_uninstall(context):
    """Post-uninstall hook - runs after addon uninstallation"""
    logger.info(f"POST-UNINSTALL: {context.addon_name}")
    
    if context.env:
        # Example: Clean up remaining data
        logger.info("Cleaning up addon data...")
        
        try:
            await context.env.cr.execute(
                "DELETE FROM ir_config_parameter WHERE key LIKE $1",
                "example_hooks.%"
            )
            logger.info("Cleanup completed successfully")
        except Exception as e:
            logger.warning(f"Could not clean up data: {e}")


@pre_upgrade_hook('example_hooks', priority=10)
async def pre_upgrade(context):
    """Pre-upgrade hook - runs before addon upgrade"""
    logger.info(f"PRE-UPGRADE: {context.addon_name}")
    
    if context.env:
        # Example: Prepare for upgrade
        logger.info("Preparing for upgrade...")
        
        try:
            # Example: Check current version
            result = await context.env.cr.fetchval(
                "SELECT value FROM ir_config_parameter WHERE key = $1",
                "example_hooks.version"
            )
            logger.info(f"Current version: {result or 'unknown'}")
        except Exception as e:
            logger.warning(f"Could not check version: {e}")


@post_upgrade_hook('example_hooks', priority=10)
async def post_upgrade(context):
    """Post-upgrade hook - runs after addon upgrade"""
    logger.info(f"POST-UPGRADE: {context.addon_name}")
    
    if context.env:
        # Example: Update version info
        logger.info("Updating version info...")
        
        try:
            await context.env.cr.execute(
                "INSERT INTO ir_config_parameter (key, value) VALUES ($1, $2) ON CONFLICT (key) DO UPDATE SET value = $2",
                "example_hooks.version", "1.0.0"
            )
            logger.info("Version info updated successfully")
        except Exception as e:
            logger.warning(f"Could not update version info: {e}")


def initialize():
    """Standard initialization function"""
    logger.info("Example hooks addon initialized")


def cleanup():
    """Standard cleanup function"""
    logger.info("Example hooks addon cleaned up")
