"""
Database management routes
"""
from fastapi import APIRouter, Request, HTTPException
from typing import Dict, Any
import re

from ..config import config
from ..database.registry import DatabaseRegistry
from ..utils.responses import APIResponse, handle_database_error

router = APIRouter(prefix="/api/databases", tags=["database"])


@router.get("")
async def list_databases_api():
    """API endpoint to list available databases"""
    print("DEBUG: Entering list_databases_api")
    if not config.list_db:
        raise HTTPException(status_code=403, detail="Database listing is disabled")

    try:
        print("DEBUG: Starting database listing logic")
        db_info_list = []

        if config.is_multi_db_mode:
            # Multi-database mode: list all databases with filter applied
            databases = await DatabaseRegistry.list_databases()

            for db_name in databases:
                # Apply database filter if configured
                if config.db_filter:
                    if not re.match(config.db_filter, db_name):
                        continue

                # Get database size and other info
                try:
                    db_manager = await DatabaseRegistry.get_database('postgres')
                    size_query = f"SELECT pg_size_pretty(pg_database_size('{db_name}')) as size"
                    size_result = await db_manager.fetch(size_query)
                    size = size_result[0]['size'] if size_result else 'Unknown'

                    # Get creation date (approximate)
                    created_query = f"""
                        SELECT (pg_stat_file('base/'||oid||'/PG_VERSION')).modification as created
                        FROM pg_database WHERE datname = '{db_name}'
                    """
                    created_result = await db_manager.fetch(created_query)
                    created = created_result[0]['created'].isoformat() if created_result else None

                except Exception as e:
                    print(f"Error getting info for database {db_name}: {e}")
                    size = 'Unknown'
                    created = None

                db_info_list.append({
                    'name': db_name,
                    'size': size,
                    'created': created,
                    'owner': 'erp',  # Default owner
                    'encoding': 'UTF8',  # Default encoding
                    'status': 'active'  # Default status
                })
        else:
            # Single database mode: show only the configured database
            db_name = config.get_default_database()
            if db_name:
                # Apply filter even in single database mode
                if config.db_filter:
                    if re.match(config.db_filter, db_name):
                        try:
                            db_manager = await DatabaseRegistry.get_database('postgres')
                            size_query = f"SELECT pg_size_pretty(pg_database_size('{db_name}')) as size"
                            size_result = await db_manager.fetch(size_query)
                            size = size_result[0]['size'] if size_result else 'Unknown'
                        except Exception:
                            size = 'Unknown'

                        db_info_list.append({
                            'name': db_name,
                            'size': size,
                            'created': None,
                            'owner': 'erp',
                            'encoding': 'UTF8',
                            'status': 'active'
                        })
                else:
                    # No filter, show the single database
                    try:
                        db_manager = await DatabaseRegistry.get_database('postgres')
                        size_query = f"SELECT pg_size_pretty(pg_database_size('{db_name}')) as size"
                        size_result = await db_manager.fetch(size_query)
                        size = size_result[0]['size'] if size_result else 'Unknown'
                    except Exception:
                        size = 'Unknown'

                    db_info_list.append({
                        'name': db_name,
                        'size': size,
                        'created': None,
                        'owner': 'erp',
                        'encoding': 'UTF8',
                        'status': 'active'
                    })

        return APIResponse.success(db_info_list)

    except Exception as e:
        print(f"Error in list_databases_api: {e}")
        import traceback
        traceback.print_exc()
        return APIResponse.error(f"Database listing failed: {str(e)}", status_code=500)


@router.post("")
async def create_database_api(request: Request):
    """API endpoint to create a new database"""
    if not config.list_db:
        raise HTTPException(status_code=403, detail="Database creation is disabled")

    try:
        data = await request.json()
        db_name = data.get('name')
        language = data.get('language', 'en_US')
        demo = data.get('demo', False)

        if not db_name:
            raise HTTPException(status_code=400, detail="Database name is required")

        # Validate database name
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', db_name):
            raise HTTPException(status_code=400, detail="Invalid database name format")

        # Apply database filter if configured
        if config.db_filter:
            if not re.match(config.db_filter, db_name):
                raise HTTPException(status_code=400, detail="Database name does not match filter")

        # Create database
        db_manager = await DatabaseRegistry.get_database('postgres')
        create_query = f'CREATE DATABASE "{db_name}" OWNER erp'
        await db_manager.execute(create_query)

        return APIResponse.success({
            'name': db_name,
            'message': 'Database created successfully'
        })

    except HTTPException:
        raise
    except Exception as e:
        return handle_database_error(e)


@router.delete("/{db_name}")
async def delete_database_api(db_name: str):
    """API endpoint to delete a database"""
    if not config.list_db:
        raise HTTPException(status_code=403, detail="Database deletion is disabled")

    try:
        # Validate database name
        if not db_name or db_name in ['postgres', 'template0', 'template1']:
            raise HTTPException(status_code=400, detail="Cannot delete system database")

        # Apply database filter if configured
        if config.db_filter:
            if not re.match(config.db_filter, db_name):
                raise HTTPException(status_code=400, detail="Database not accessible")

        # Drop database
        db_manager = await DatabaseRegistry.get_database('postgres')

        # Terminate connections to the database first
        terminate_query = f"""
            SELECT pg_terminate_backend(pid)
            FROM pg_stat_activity
            WHERE datname = '{db_name}' AND pid <> pg_backend_pid()
        """
        await db_manager.execute(terminate_query)

        # Drop the database
        drop_query = f'DROP DATABASE IF EXISTS "{db_name}"'
        await db_manager.execute(drop_query)

        return APIResponse.success({
            'name': db_name,
            'message': 'Database deleted successfully'
        })

    except HTTPException:
        raise
    except Exception as e:
        return handle_database_error(e)