# Addon Lifecycle Hooks and Environment System

This document describes the addon lifecycle hooks system and Environment context management in the ERP system.

## Overview

The ERP system now provides:

1. **AsyncLocalStorage-like context management** using Python's `contextvars`
2. **Odoo-like Environment** with `cr`, `uid`, and `context` properties
3. **Addon lifecycle hooks** for install, uninstall, and upgrade operations
4. **Automatic Environment setup** via middleware for each HTTP request

## AsyncLocalStorage Context System

### Basic Usage

```python
from erp.context import ContextManager, AsyncLocalStorage

# Create a custom context storage
my_storage = AsyncLocalStorage('my_context')

# Set and get values
token = my_storage.set("my_value")
value = my_storage.get()  # Returns "my_value"

# Use context manager
async with my_storage.run("my_value"):
    value = my_storage.get()  # Returns "my_value"
```

### Global Context Manager

```python
from erp.context import ContextManager

# Get current environment
env = ContextManager.get_environment()

# Get current database
db_name = ContextManager.get_database()

# Get current user
user_id = ContextManager.get_user()

# Set context
async with ContextManager.with_context(env=env, custom_key="value"):
    # Code runs with context
    pass
```

## Environment System

### Creating Environments

```python
from erp.environment import create_env, EnvironmentManager

# Create environment
env = await create_env('my_database', user_id=1, context={'lang': 'en_US'})

# Or using the manager
env = await EnvironmentManager.create_environment('my_database', 1, {'test': True})
```

### Environment Properties

```python
# Database cursor
cr = env.cr
await cr.execute("SELECT * FROM res_users")
result = await cr.fetch("SELECT * FROM res_users")

# User ID
uid = env.uid  # Returns integer

# Context dictionary
context = env.context  # Returns dict copy

# Create new environment with different context
new_env = env.with_context(debug=True, test_mode=True)

# Create new environment with different user
admin_env = env.with_user(1)
```

### Using Environment in Context

```python
from erp.environment import env

# Get current environment from context
current_env = env()

# Use environment context manager
async with EnvironmentManager.with_environment('db_name', 1, {'test': True}):
    # Code runs with environment in context
    current_env = env()
```

## Addon Lifecycle Hooks

### Available Hook Types

- `PRE_INSTALL` - Before addon installation
- `POST_INSTALL` - After addon installation
- `PRE_UNINSTALL` - Before addon uninstallation
- `POST_UNINSTALL` - After addon uninstallation
- `PRE_UPGRADE` - Before addon upgrade
- `POST_UPGRADE` - After addon upgrade

### Defining Hooks

```python
from erp.addons.hooks import (
    pre_install_hook, post_install_hook,
    pre_uninstall_hook, post_uninstall_hook,
    pre_upgrade_hook, post_upgrade_hook
)

@pre_install_hook('my_addon', priority=10)
async def my_pre_install(context):
    """Pre-install hook"""
    print(f"Installing {context.addon_name}")
    
    if context.env:
        # Access database
        result = await context.env.cr.fetchval("SELECT COUNT(*) FROM res_users")
        print(f"Found {result} users")

@post_install_hook('my_addon')
def my_post_install(context):
    """Post-install hook (can be sync or async)"""
    print(f"Installed {context.addon_name}")
    
    # Access environment
    if context.env:
        print(f"Database: {context.env.cr.db_name}")
        print(f"User: {context.env.uid}")
        print(f"Context: {context.env.context}")
```

### Hook Context

Each hook receives a `HookContext` object with:

- `addon_name` - Name of the addon
- `hook_type` - Type of hook (HookType enum)
- `env` - Environment instance (may be None)
- `data` - Additional data passed to the hook

### Hook Priority

Hooks are executed in priority order (lower number = higher priority):

```python
@pre_install_hook('my_addon', priority=1)  # Runs first
async def high_priority_hook(context):
    pass

@pre_install_hook('my_addon', priority=50)  # Runs second (default priority)
async def normal_priority_hook(context):
    pass

@pre_install_hook('my_addon', priority=100)  # Runs last
async def low_priority_hook(context):
    pass
```

## Middleware Integration

The system automatically sets up Environment context for each HTTP request via middleware.

### Request Headers

The middleware recognizes these headers:

- `X-Database` - Database name (multi-db mode)
- `X-User-ID` - User ID
- `X-Timezone` - User timezone
- `Accept-Language` - User language
- `X-Context-*` - Custom context values

### Example Request

```bash
curl -H "X-Database: my_db" \
     -H "X-User-ID: 1" \
     -H "X-Timezone: UTC" \
     -H "Accept-Language: en-US" \
     -H "X-Context-debug: true" \
     http://localhost:8069/api/users
```

### Accessing Environment in Handlers

```python
from fastapi import Request

async def my_handler(request: Request):
    # Environment is automatically available
    env = request.state.env
    
    # Or get from context
    from erp.environment import env
    current_env = env()
    
    # Use environment
    users = await current_env.cr.fetch("SELECT * FROM res_users")
    return {"users": users}
```

## Example Addon

See `addons/example_hooks/` for a complete example demonstrating:

- All lifecycle hook types
- Database operations in hooks
- Environment usage
- Error handling

## Testing

Run the test script to see the system in action:

```bash
python test_lifecycle_hooks.py
```

This will test:
- Environment creation and context management
- Lifecycle hook execution
- Addon loading with hooks

## Best Practices

1. **Always check if environment is available** in hooks:
   ```python
   if context.env:
       # Use environment
   ```

2. **Handle errors gracefully** in hooks:
   ```python
   try:
       await context.env.cr.execute("...")
   except Exception as e:
       logger.warning(f"Hook failed: {e}")
   ```

3. **Use appropriate priorities** for hooks that depend on each other

4. **Keep hooks lightweight** - they run during addon operations

5. **Use transactions** for database operations:
   ```python
   async with EnvironmentManager.transaction(context.env):
       # Database operations
   ```
