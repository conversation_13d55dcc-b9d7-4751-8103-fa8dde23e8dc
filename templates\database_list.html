<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP System - Database Selection</title>
    <link rel="stylesheet" href="/static/css/database_list.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-database"></i>
                <h1>ERP System</h1>
            </div>
            <div class="subtitle">Select a database to continue</div>
        </header>

        <main class="main-content">
            <div class="database-grid" id="databaseGrid">
                <!-- Database cards will be populated by JavaScript -->
            </div>

            <div class="actions">
                <button class="btn btn-primary" onclick="refreshDatabases()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
                <button class="btn btn-secondary" onclick="showCreateDatabase()">
                    <i class="fas fa-plus"></i>
                    Create Database
                </button>
            </div>
        </main>

        <!-- Loading spinner -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Loading databases...</p>
        </div>

        <!-- Error message -->
        <div class="error-message" id="errorMessage" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorText"></span>
            <button onclick="hideError()" class="close-error">×</button>
        </div>

        <!-- Create Database Modal -->
        <div class="modal" id="createDbModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Create New Database</h3>
                    <button onclick="hideCreateDatabase()" class="close-modal">×</button>
                </div>
                <div class="modal-body">
                    <form id="createDbForm">
                        <div class="form-group">
                            <label for="dbName">Database Name:</label>
                            <input type="text" id="dbName" name="dbName" required 
                                   pattern="^[a-zA-Z][a-zA-Z0-9_]*$" 
                                   placeholder="e.g., erp_production">
                            <small>Must start with a letter and contain only letters, numbers, and underscores</small>
                        </div>
                        <div class="form-group">
                            <label for="dbLanguage">Language:</label>
                            <select id="dbLanguage" name="dbLanguage">
                                <option value="en_US">English (US)</option>
                                <option value="en_GB">English (UK)</option>
                                <option value="fr_FR">French</option>
                                <option value="de_DE">German</option>
                                <option value="es_ES">Spanish</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="loadDemo" name="loadDemo">
                                Load demo data
                            </label>
                        </div>
                        <div class="form-actions">
                            <button type="button" onclick="hideCreateDatabase()" class="btn btn-secondary">Cancel</button>
                            <button type="submit" class="btn btn-primary">Create Database</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/database_list.js"></script>
</body>
</html>
