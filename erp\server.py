"""
FastAPI ASGI Server for ERP system - Modularized version
"""
from fastapi import FastAPI

from .addons.loader import <PERSON><PERSON><PERSON>oa<PERSON>
from .server_config import ServerConfig
from .routes import api, database, system, app
from .utils.responses import APIResponse


class ERPServer:
    """Main ERP server application"""

    def __init__(self):
        self.addon_loader = AddonLoader()
        self.server_config = ServerConfig(self.addon_loader)
        self.app = self._create_app()
        self._setup_server()
        self._setup_routes()
    
    def _create_app(self) -> FastAPI:
        """Create FastAPI application"""
        return self.server_config.create_app()
    
    def _setup_server(self):
        """Setup server configuration"""
        self.server_config.setup_static_files(self.app)
        self.server_config.setup_middleware(self.app)
    
    def _setup_routes(self):
        """Setup API routes using modular routers"""
        # Include route modules
        self.app.include_router(system.router)
        self.app.include_router(api.router)
        self.app.include_router(database.router)
        self.app.include_router(app.router)
        
        # Update system routes with addon information
        self._update_system_routes()
    
    def _update_system_routes(self):
        """Update system routes with server-specific data"""
        # Override the addons endpoint to provide actual addon data
        @self.app.get("/addons")
        async def list_addons():
            """List all loaded addons"""
            return APIResponse.success(self.server_config.get_addon_info())
        
        # Update health check to include addon count
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            try:
                from .database.registry import DatabaseRegistry
                
                # Check database connection
                db = await DatabaseRegistry.get_current_database()
                if db:
                    await db.execute("SELECT 1")
                    db_status = "connected"
                else:
                    db_status = "no_connection"
                
                return APIResponse.success({
                    "status": "healthy",
                    "database": db_status,
                    "addons": len(self.addon_loader.loaded_addons) if self.addon_loader else 0
                })
            except Exception as e:
                return APIResponse.error(f"Health check failed: {str(e)}", status_code=503)


# Create global server instance
server = ERPServer()
app = server.app


def create_app():
    """Create and return the FastAPI application"""
    return app
